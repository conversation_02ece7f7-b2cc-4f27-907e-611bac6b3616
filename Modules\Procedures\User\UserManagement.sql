-- UserManagement.sql
-- Contains queries for user management operations

-- [DeleteUser] --
DELETE FROM users
WHERE user_id = @user_id;
-- [End] --

-- [CheckUsernameExists] --
SELECT COUNT(*) as count
FROM users
WHERE username = @username
AND (@exclude_user_id IS NULL OR user_id != @exclude_user_id);
-- [End] --

-- [CheckEmailExists] --
SELECT COUNT(*) as count
FROM users
WHERE email = @email
AND (@exclude_user_id IS NULL OR user_id != @exclude_user_id);
-- [End] --

-- [GetUserCount] --
SELECT COUNT(*) as total_count
FROM users;
-- [End] --

-- [GetActiveUserCount] --
SELECT COUNT(*) as active_count
FROM users
WHERE is_active = TRUE;
-- [End] --

-- [GetUsersByRole] --
SELECT
    user_id,
    username,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    can_read,
    can_create,
    can_edit,
    can_delete,
    can_print
FROM
    users
WHERE
    role = @role
ORDER BY
    full_name ASC;
-- [End] --

-- [DeactivateUser] --
UPDATE users
SET
    is_active = FALSE
WHERE
    user_id = @user_id;
-- [End] --

-- [ActivateUser] --
UPDATE users
SET
    is_active = TRUE
WHERE
    user_id = @user_id;
-- [End] --

-- [GetDistinctRoles] --
SELECT DISTINCT role
FROM users
WHERE role IS NOT NULL
ORDER BY role ASC;
-- [End] --
