using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Security tests for Task 6: Testing Strategy and Scenarios
    /// Comprehensive security validation and penetration testing
    /// </summary>
    [TestClass]
    public class SecurityTestSuite
    {
        [TestInitialize]
        public void Setup()
        {
            SyncLoggingService.ConfigureRollingFileLogger();
        }

        #region API Key Security Tests

        [TestMethod]
        public async Task Security_HealthCheck_APIKeyValidation()
        {
            Console.WriteLine("Testing API key security validation...");

            try
            {
                // Test various invalid API keys
                var invalidKeys = new[]
                {
                    null,
                    "",
                    "wrong-key",
                    "admin",
                    "password",
                    "123456",
                    "default",
                    "test",
                    "api-key",
                    "secret"
                };

                foreach (var invalidKey in invalidKeys)
                {
                    var result = await HealthCheckService.GetSyncHealthStatus(invalidKey, "localhost");
                    Assert.AreEqual("Unauthorized", result.Status, 
                        $"Invalid API key '{invalidKey}' should be rejected");
                    Assert.IsNotNull(result.Message, "Should provide error message for invalid key");
                }

                // Test valid API key
                var validResult = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost");
                Assert.AreNotEqual("Unauthorized", validResult.Status, "Valid API key should be accepted");

                Console.WriteLine("✅ API key validation test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ API key security test failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public async Task Security_HealthCheck_HostRestriction()
        {
            Console.WriteLine("Testing host restriction security...");

            try
            {
                // Test various non-localhost hosts
                var forbiddenHosts = new[]
                {
                    "***********",
                    "********",
                    "example.com",
                    "google.com",
                    "malicious-host.com",
                    "remote-server",
                    "*************",
                    "**********"
                };

                foreach (var host in forbiddenHosts)
                {
                    var result = await HealthCheckService.GetSyncHealthStatus("default-dev-key", host);
                    Assert.AreEqual("Forbidden", result.Status, 
                        $"Non-localhost host '{host}' should be forbidden");
                    Assert.IsNotNull(result.Message, "Should provide error message for forbidden host");
                }

                // Test allowed localhost variants
                var allowedHosts = new[] { "localhost", "127.0.0.1", "::1" };
                foreach (var host in allowedHosts)
                {
                    var result = await HealthCheckService.GetSyncHealthStatus("default-dev-key", host);
                    Assert.AreNotEqual("Forbidden", result.Status, 
                        $"Localhost variant '{host}' should be allowed");
                }

                Console.WriteLine("✅ Host restriction test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Host restriction security test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region SQL Injection Prevention Tests

        [TestMethod]
        public void Security_FormNameValidation_SQLInjectionPrevention()
        {
            Console.WriteLine("Testing SQL injection prevention...");

            try
            {
                // Common SQL injection patterns
                var sqlInjectionAttempts = new[]
                {
                    "'; DROP TABLE users; --",
                    "' OR '1'='1",
                    "'; DELETE FROM permissions; --",
                    "' UNION SELECT * FROM users --",
                    "'; INSERT INTO users VALUES ('hacker', 'admin'); --",
                    "' OR 1=1 --",
                    "'; EXEC xp_cmdshell('dir'); --",
                    "' AND (SELECT COUNT(*) FROM users) > 0 --",
                    "'; UPDATE users SET password='hacked'; --",
                    "' OR EXISTS(SELECT * FROM users WHERE username='admin') --"
                };

                foreach (var injection in sqlInjectionAttempts)
                {
                    var isValid = PermissionSyncService.ValidateFormName(injection);
                    Assert.IsFalse(isValid, $"SQL injection attempt should be rejected: {injection}");
                }

                Console.WriteLine("✅ SQL injection prevention test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SQL injection prevention test failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public void Security_FormNameValidation_PathTraversalPrevention()
        {
            Console.WriteLine("Testing path traversal prevention...");

            try
            {
                // Path traversal attack patterns
                var pathTraversalAttempts = new[]
                {
                    "../../../etc/passwd",
                    "..\\..\\..\\windows\\system32\\config\\sam",
                    "../../../../boot.ini",
                    "../../../var/log/auth.log",
                    "..\\..\\..\\autoexec.bat",
                    "../../../../etc/shadow",
                    "../../../proc/version",
                    "..\\..\\..\\windows\\win.ini"
                };

                foreach (var traversal in pathTraversalAttempts)
                {
                    var isValid = PermissionSyncService.ValidateFormName(traversal);
                    Assert.IsFalse(isValid, $"Path traversal attempt should be rejected: {traversal}");
                }

                Console.WriteLine("✅ Path traversal prevention test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Path traversal prevention test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Input Validation Security Tests

        [TestMethod]
        public void Security_FormNameValidation_ScriptInjectionPrevention()
        {
            Console.WriteLine("Testing script injection prevention...");

            try
            {
                // Script injection patterns
                var scriptInjectionAttempts = new[]
                {
                    "<script>alert('xss')</script>",
                    "javascript:alert('xss')",
                    "<img src=x onerror=alert('xss')>",
                    "<svg onload=alert('xss')>",
                    "eval('malicious code')",
                    "<iframe src='javascript:alert(1)'></iframe>",
                    "onmouseover='alert(1)'",
                    "<body onload=alert('xss')>"
                };

                foreach (var script in scriptInjectionAttempts)
                {
                    var isValid = PermissionSyncService.ValidateFormName(script);
                    Assert.IsFalse(isValid, $"Script injection attempt should be rejected: {script}");
                }

                Console.WriteLine("✅ Script injection prevention test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Script injection prevention test failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public void Security_FormNameValidation_SpecialCharacterHandling()
        {
            Console.WriteLine("Testing special character security handling...");

            try
            {
                // Potentially dangerous special characters
                var dangerousChars = new[]
                {
                    "Form|Name",
                    "Form&Name",
                    "Form$Name",
                    "Form`Name",
                    "Form~Name",
                    "Form!Name",
                    "Form@Name",
                    "Form#Name",
                    "Form%Name",
                    "Form^Name",
                    "Form*Name",
                    "Form(Name)",
                    "Form[Name]",
                    "Form{Name}",
                    "Form=Name",
                    "Form+Name"
                };

                foreach (var dangerousName in dangerousChars)
                {
                    var isValid = PermissionSyncService.ValidateFormName(dangerousName);
                    // Most special characters should be rejected for security
                    if (dangerousName.Contains("|") || dangerousName.Contains("&") || 
                        dangerousName.Contains("$") || dangerousName.Contains("`"))
                    {
                        Assert.IsFalse(isValid, $"Dangerous character should be rejected: {dangerousName}");
                    }
                }

                Console.WriteLine("✅ Special character handling test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Special character handling test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Buffer Overflow Prevention Tests

        [TestMethod]
        public void Security_FormNameValidation_BufferOverflowPrevention()
        {
            Console.WriteLine("Testing buffer overflow prevention...");

            try
            {
                // Test extremely long form names
                var longNames = new[]
                {
                    new string('A', 101),  // Just over limit
                    new string('B', 500),  // Much longer
                    new string('C', 1000), // Very long
                    new string('D', 5000)  // Extremely long
                };

                foreach (var longName in longNames)
                {
                    var isValid = PermissionSyncService.ValidateFormName(longName);
                    Assert.IsFalse(isValid, $"Overly long form name should be rejected (length: {longName.Length})");
                }

                // Test maximum allowed length
                var maxLengthName = new string('Z', 100); // Exactly at limit
                var maxIsValid = PermissionSyncService.ValidateFormName(maxLengthName);
                Assert.IsTrue(maxIsValid, "Form name at maximum length should be accepted");

                Console.WriteLine("✅ Buffer overflow prevention test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Buffer overflow prevention test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Access Control Tests

        [TestMethod]
        public async Task Security_HealthCheck_CombinedSecurityValidation()
        {
            Console.WriteLine("Testing combined security validation...");

            try
            {
                // Test combinations of invalid credentials and hosts
                var testCases = new[]
                {
                    new { Key = "wrong-key", Host = "remote-host", ExpectedStatus = "Forbidden" },
                    new { Key = "wrong-key", Host = "localhost", ExpectedStatus = "Unauthorized" },
                    new { Key = "", Host = "localhost", ExpectedStatus = "Unauthorized" },
                    new { Key = (string)null, Host = "localhost", ExpectedStatus = "Unauthorized" },
                    new { Key = "default-dev-key", Host = "remote-host", ExpectedStatus = "Forbidden" },
                    new { Key = "default-dev-key", Host = "", ExpectedStatus = "Forbidden" },
                    new { Key = "default-dev-key", Host = (string)null, ExpectedStatus = "Forbidden" }
                };

                foreach (var testCase in testCases)
                {
                    var result = await HealthCheckService.GetSyncHealthStatus(testCase.Key, testCase.Host);
                    Assert.AreEqual(testCase.ExpectedStatus, result.Status,
                        $"Security validation failed for Key='{testCase.Key}', Host='{testCase.Host}'");
                }

                Console.WriteLine("✅ Combined security validation test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Combined security validation test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Security Logging Tests

        [TestMethod]
        public async Task Security_EventLogging_SecurityEvents()
        {
            Console.WriteLine("Testing security event logging...");

            try
            {
                // Trigger various security events that should be logged
                await HealthCheckService.GetSyncHealthStatus("invalid-key", "localhost");
                await HealthCheckService.GetSyncHealthStatus("default-dev-key", "remote-host");
                
                PermissionSyncService.ValidateFormName("'; DROP TABLE users; --");
                PermissionSyncService.ValidateFormName("<script>alert('xss')</script>");
                PermissionSyncService.ValidateFormName(new string('A', 200));

                // Note: In a real implementation, we would verify that these events
                // are actually logged to the security log file. For now, we just
                // ensure the operations complete without throwing exceptions.

                Console.WriteLine("✅ Security event logging test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Security event logging test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        /// <summary>
        /// Run all security tests
        /// </summary>
        public static void RunAllSecurityTests()
        {
            Console.WriteLine("=== Security Test Suite ===");
            
            var testSuite = new SecurityTestSuite();
            
            try
            {
                testSuite.Setup();
                
                testSuite.Security_HealthCheck_APIKeyValidation().Wait();
                testSuite.Security_HealthCheck_HostRestriction().Wait();
                testSuite.Security_FormNameValidation_SQLInjectionPrevention();
                testSuite.Security_FormNameValidation_PathTraversalPrevention();
                testSuite.Security_FormNameValidation_ScriptInjectionPrevention();
                testSuite.Security_FormNameValidation_SpecialCharacterHandling();
                testSuite.Security_FormNameValidation_BufferOverflowPrevention();
                testSuite.Security_HealthCheck_CombinedSecurityValidation().Wait();
                testSuite.Security_EventLogging_SecurityEvents().Wait();
                
                Console.WriteLine("✅ All security tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Security tests failed: {ex.Message}");
                throw;
            }
        }
    }
}
