using System;
using System.Diagnostics;
using System.Threading;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace ProManage.Modules.UI
{
    /// <summary>
    /// Centralized service for controlling the progress indicator in the MainFrame.
    /// Provides a singleton pattern for showing/hiding progress indicators specifically during database operations.
    /// Ensures minimum display time for better user experience and thread-safe operations.
    /// </summary>
    /// <example>
    /// Basic usage pattern for database operations:
    /// <code>
    /// ProgressIndicatorService.Instance.ShowProgress();
    /// try
    /// {
    ///     // Database operation (CRUD, search, etc.)
    ///     var result = await DatabaseRepository.GetDataAsync();
    /// }
    /// finally
    /// {
    ///     ProgressIndicatorService.Instance.HideProgress();
    /// }
    /// </code>
    /// </example>
    public class ProgressIndicatorService
    {
        #region Private Fields

        // Singleton instance
        private static ProgressIndicatorService _instance;
        private static readonly object _lockObject = new object();
        private readonly object _operationLock = new object();

        // Reference to the MainFrame's progress bar
        private MarqueeProgressBarControl _progressBar;

        // Track when the progress indicator was shown
        private DateTime _showTime = DateTime.MinValue;

        // Track if the service is initialized
        private bool _isInitialized = false;

        // Reference counting for nested operations
        private int _operationCount = 0;

        #endregion

        #region Constructor

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private ProgressIndicatorService()
        {
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the singleton instance of the ProgressIndicatorService
        /// </summary>
        public static ProgressIndicatorService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new ProgressIndicatorService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets whether the service has been properly initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets whether the progress indicator is currently visible
        /// </summary>
        public bool IsVisible => _progressBar?.Visible ?? false;

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the service with a reference to the MainFrame's progress bar.
        /// This method must be called during application startup before using the service.
        /// </summary>
        /// <param name="progressBar">The MainFrame's progress bar control</param>
        /// <exception cref="ArgumentNullException">Thrown when progressBar is null</exception>
        public void Initialize(MarqueeProgressBarControl progressBar)
        {
            if (progressBar == null)
            {
                throw new ArgumentNullException(nameof(progressBar), "Progress bar control cannot be null");
            }

            lock (_lockObject)
            {
                _progressBar = progressBar;
                _isInitialized = true;
                Debug.WriteLine("ProgressIndicatorService initialized successfully");
            }
        }

        /// <summary>
        /// Shows the progress indicator for database operations.
        /// Supports nested database operations through reference counting.
        /// </summary>
        /// <param name="message">Optional message for debugging purposes</param>
        public void ShowProgress(string message = null)
        {
            if (!_isInitialized)
            {
                Debug.WriteLine("Warning: ProgressIndicatorService not initialized. Call Initialize() first.");
                return;
            }

            lock (_operationLock)
            {
                try
                {
                    _operationCount++;

                    // Get stack trace for debugging
                    var stackTrace = new System.Diagnostics.StackTrace(1, true);
                    var callingMethod = stackTrace.GetFrame(0)?.GetMethod()?.Name ?? "Unknown";
                    var callingClass = stackTrace.GetFrame(0)?.GetMethod()?.DeclaringType?.Name ?? "Unknown";

                    // Only show the progress bar on the first operation
                    if (_operationCount == 1 && _progressBar != null)
                    {
                        // Record the time when the progress bar is shown
                        _showTime = DateTime.Now;

                        // Ensure we're on the UI thread
                        if (_progressBar.InvokeRequired)
                        {
                            _progressBar.Invoke(new Action(() => _progressBar.Visible = true));
                        }
                        else
                        {
                            _progressBar.Visible = true;
                        }

                        // Force the UI to update immediately
                        System.Windows.Forms.Application.DoEvents();

                        Debug.WriteLine($"[PROGRESS] SHOWN by {callingClass}.{callingMethod}() - Count: {_operationCount}" +
                                      (string.IsNullOrEmpty(message) ? "" : $" - {message}"));
                    }
                    else
                    {
                        Debug.WriteLine($"[PROGRESS] ALREADY VISIBLE - Count: {_operationCount}, Called by {callingClass}.{callingMethod}()" +
                                      (string.IsNullOrEmpty(message) ? "" : $" - {message}"));
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[PROGRESS] ERROR showing progress indicator: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Hides the progress indicator after database operations complete.
        /// Uses reference counting to handle nested database operations properly.
        /// Ensures minimum display time of 250ms for better user experience.
        /// </summary>
        public void HideProgress()
        {
            if (!_isInitialized)
            {
                Debug.WriteLine("[PROGRESS] HideProgress called but service not initialized");
                return;
            }

            lock (_operationLock)
            {
                try
                {
                    // Get stack trace for debugging
                    var stackTrace = new System.Diagnostics.StackTrace(1, true);
                    var callingMethod = stackTrace.GetFrame(0)?.GetMethod()?.Name ?? "Unknown";
                    var callingClass = stackTrace.GetFrame(0)?.GetMethod()?.DeclaringType?.Name ?? "Unknown";

                    if (_operationCount > 0)
                    {
                        _operationCount--;
                    }

                    Debug.WriteLine($"[PROGRESS] HIDE called by {callingClass}.{callingMethod}() - Count: {_operationCount}");

                    // Only hide the progress bar when all operations are complete
                    if (_operationCount == 0 && _progressBar != null && _progressBar.Visible)
                    {
                        // Calculate how long the progress bar has been visible
                        TimeSpan elapsedTime = DateTime.Now - _showTime;

                        // If it's been visible for less than 250ms, add a small delay
                        // to ensure it's visible long enough for the user to see it
                        if (elapsedTime.TotalMilliseconds < 250)
                        {
                            int sleepTime = 250 - (int)elapsedTime.TotalMilliseconds;
                            if (sleepTime > 0)
                            {
                                Thread.Sleep(sleepTime);
                            }
                        }

                        // Ensure we're on the UI thread
                        if (_progressBar.InvokeRequired)
                        {
                            _progressBar.Invoke(new Action(() => _progressBar.Visible = false));
                        }
                        else
                        {
                            _progressBar.Visible = false;
                        }

                        Debug.WriteLine($"[PROGRESS] HIDDEN by {callingClass}.{callingMethod}() - All operations complete");
                    }
                    else if (_operationCount > 0)
                    {
                        Debug.WriteLine($"[PROGRESS] STILL NEEDED - Remaining operations: {_operationCount}");
                    }
                    else if (_progressBar == null || !_progressBar.Visible)
                    {
                        Debug.WriteLine($"[PROGRESS] Already hidden or progress bar null");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[PROGRESS] ERROR hiding progress indicator: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Resets the operation count. Use with caution - typically only needed for error recovery.
        /// </summary>
        public void Reset()
        {
            lock (_operationLock)
            {
                // Get stack trace for debugging
                var stackTrace = new System.Diagnostics.StackTrace(1, true);
                var callingMethod = stackTrace.GetFrame(0)?.GetMethod()?.Name ?? "Unknown";
                var callingClass = stackTrace.GetFrame(0)?.GetMethod()?.DeclaringType?.Name ?? "Unknown";

                var previousCount = _operationCount;
                _operationCount = 0;

                if (_progressBar != null && _progressBar.Visible)
                {
                    try
                    {
                        if (_progressBar.InvokeRequired)
                        {
                            _progressBar.Invoke(new Action(() => _progressBar.Visible = false));
                        }
                        else
                        {
                            _progressBar.Visible = false;
                        }
                        Debug.WriteLine($"[PROGRESS] RESET by {callingClass}.{callingMethod}() - Previous count: {previousCount}, Progress bar hidden");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[PROGRESS] ERROR resetting progress indicator: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine($"[PROGRESS] RESET by {callingClass}.{callingMethod}() - Previous count: {previousCount}, Progress bar was already hidden");
                }
            }
        }

        /// <summary>
        /// Gets the current operation count for debugging purposes
        /// </summary>
        public int GetOperationCount()
        {
            lock (_operationLock)
            {
                return _operationCount;
            }
        }

        #endregion
    }
}
