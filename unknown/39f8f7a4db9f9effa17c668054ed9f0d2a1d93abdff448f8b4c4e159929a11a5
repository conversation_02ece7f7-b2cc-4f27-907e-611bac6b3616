using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Services;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Simple test runner to check if our background status implementation is working
    /// </summary>
    public static class TestRunner
    {
        public static void RunBasicTests()
        {
            Console.WriteLine("=== Background Status Implementation Test Runner ===");
            
            var testResults = new List<(string TestName, bool Passed, string Error)>();
            
            // Test 1: StatusProgressService accessibility
            try
            {
                var message = StatusProgressService.FormDiscoveryMessages.Initializing;
                var passed = !string.IsNullOrEmpty(message) && message.Contains("form discovery");
                testResults.Add(("StatusProgressService_Accessibility", passed, passed ? null : "Message is null or doesn't contain expected text"));
                Console.WriteLine($"✓ Test 1: StatusProgressService accessibility - {(passed ? "PASSED" : "FAILED")}");
            }
            catch (Exception ex)
            {
                testResults.Add(("StatusProgressService_Accessibility", false, ex.Message));
                Console.WriteLine($"✗ Test 1: StatusProgressService accessibility - FAILED: {ex.Message}");
            }
            
            // Test 2: FormDiscoveryService with progress
            try
            {
                var statusUpdates = new List<string>();
                var progress = new Progress<string>(status => statusUpdates.Add(status));
                
                var result = FormDiscoveryService.CompareFormsWithDatabase(progress);
                var passed = result != null && statusUpdates.Count > 0;
                testResults.Add(("FormDiscoveryService_WithProgress", passed, passed ? null : "Result is null or no status updates"));
                Console.WriteLine($"✓ Test 2: FormDiscoveryService with progress - {(passed ? "PASSED" : "FAILED")}");
                
                if (passed)
                {
                    Console.WriteLine($"  Status updates received: {statusUpdates.Count}");
                    foreach (var status in statusUpdates)
                    {
                        Console.WriteLine($"    - {status}");
                    }
                }
            }
            catch (Exception ex)
            {
                testResults.Add(("FormDiscoveryService_WithProgress", false, ex.Message));
                Console.WriteLine($"✗ Test 2: FormDiscoveryService with progress - FAILED: {ex.Message}");
            }
            
            // Test 3: FormDiscoveryService without progress
            try
            {
                var result = FormDiscoveryService.CompareFormsWithDatabase(null);
                var passed = result != null;
                testResults.Add(("FormDiscoveryService_WithoutProgress", passed, passed ? null : "Result is null"));
                Console.WriteLine($"✓ Test 3: FormDiscoveryService without progress - {(passed ? "PASSED" : "FAILED")}");
            }
            catch (Exception ex)
            {
                testResults.Add(("FormDiscoveryService_WithoutProgress", false, ex.Message));
                Console.WriteLine($"✗ Test 3: FormDiscoveryService without progress - FAILED: {ex.Message}");
            }
            
            // Test 4: Status history
            try
            {
                StatusProgressService.ClearHistory();
                StatusProgressService.AddToHistory("Test status", "Test operation");
                var history = StatusProgressService.GetRecentHistory(5);
                var passed = history.Count > 0;
                testResults.Add(("StatusProgressService_History", passed, passed ? null : "No history entries"));
                Console.WriteLine($"✓ Test 4: Status history - {(passed ? "PASSED" : "FAILED")}");
            }
            catch (Exception ex)
            {
                testResults.Add(("StatusProgressService_History", false, ex.Message));
                Console.WriteLine($"✗ Test 4: Status history - FAILED: {ex.Message}");
            }
            
            // Test 5: Message templates
            try
            {
                var messages = new[]
                {
                    StatusProgressService.FormDiscoveryMessages.Initializing,
                    StatusProgressService.FormDiscoveryMessages.ScanningForms,
                    StatusProgressService.SyncMessages.Starting,
                    StatusProgressService.SyncMessages.Completed
                };
                
                var allValid = true;
                foreach (var message in messages)
                {
                    if (string.IsNullOrEmpty(message) || message.Length < 5)
                    {
                        allValid = false;
                        break;
                    }
                }
                
                testResults.Add(("StatusProgressService_MessageTemplates", allValid, allValid ? null : "Some messages are invalid"));
                Console.WriteLine($"✓ Test 5: Message templates - {(allValid ? "PASSED" : "FAILED")}");
            }
            catch (Exception ex)
            {
                testResults.Add(("StatusProgressService_MessageTemplates", false, ex.Message));
                Console.WriteLine($"✗ Test 5: Message templates - FAILED: {ex.Message}");
            }
            
            // Summary
            Console.WriteLine("\n=== Test Summary ===");
            var passedCount = 0;
            var totalCount = testResults.Count;
            
            foreach (var (testName, passed, error) in testResults)
            {
                if (passed)
                {
                    passedCount++;
                    Console.WriteLine($"✓ {testName}: PASSED");
                }
                else
                {
                    Console.WriteLine($"✗ {testName}: FAILED - {error}");
                }
            }
            
            Console.WriteLine($"\nResults: {passedCount}/{totalCount} tests passed");
            
            if (passedCount == totalCount)
            {
                Console.WriteLine("🎉 All tests passed! Background status implementation is working correctly.");
            }
            else
            {
                Console.WriteLine("⚠️ Some tests failed. Please check the implementation.");
            }
        }
    }
}
