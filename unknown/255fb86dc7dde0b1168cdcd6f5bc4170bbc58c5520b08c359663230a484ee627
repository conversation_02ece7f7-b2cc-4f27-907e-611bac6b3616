using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using ProManage.Modules.Services;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Simple tests for background status functionality that don't require external dependencies
    /// </summary>
    [TestClass]
    public class SimpleBackgroundStatusTests
    {
        [TestInitialize]
        public void TestInitialize()
        {
            // Clear any existing status history
            StatusProgressService.ClearHistory();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Clean up after tests
            StatusProgressService.ClearHistory();
        }

        [TestMethod]
        public void StatusProgressService_FormatStatus_ReturnsFormattedMessage()
        {
            // Arrange
            var template = StatusProgressService.Templates.Initializing;
            var operation = "test operation";

            // Act
            var result = StatusProgressService.FormatStatus(template, operation);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("test operation"));
            Assert.IsTrue(result.Contains("Initializing"));
        }

        [TestMethod]
        public void StatusProgressService_FormatStatusWithCount_ReturnsCorrectFormat()
        {
            // Arrange
            var operation = "processing items";
            var current = 5;
            var total = 10;

            // Act
            var result = StatusProgressService.FormatStatusWithCount(operation, current, total);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("processing items"));
            Assert.IsTrue(result.Contains("5/10"));
        }

        [TestMethod]
        public void StatusProgressService_CalculatePercentage_ReturnsCorrectPercentage()
        {
            // Test cases
            Assert.AreEqual(0, StatusProgressService.CalculatePercentage(0, 100));
            Assert.AreEqual(50, StatusProgressService.CalculatePercentage(50, 100));
            Assert.AreEqual(100, StatusProgressService.CalculatePercentage(100, 100));
            Assert.AreEqual(100, StatusProgressService.CalculatePercentage(150, 100)); // Should cap at 100
            Assert.AreEqual(0, StatusProgressService.CalculatePercentage(10, 0)); // Handle division by zero
        }

        [TestMethod]
        public void StatusProgressService_AddToHistory_AddsEntrySuccessfully()
        {
            // Arrange
            var status = "Test status";
            var operation = "Test operation";

            // Act
            StatusProgressService.AddToHistory(status, operation);
            var history = StatusProgressService.GetRecentHistory(1);

            // Assert
            Assert.AreEqual(1, history.Count);
            Assert.AreEqual(status, history[0].Status);
            Assert.AreEqual(operation, history[0].Operation);
        }

        [TestMethod]
        public void StatusProgressService_GetRecentHistory_ReturnsCorrectCount()
        {
            // Arrange - Add multiple entries
            for (int i = 0; i < 15; i++)
            {
                StatusProgressService.AddToHistory($"Status {i}", $"Operation {i}");
            }

            // Act
            var history = StatusProgressService.GetRecentHistory(10);

            // Assert
            Assert.AreEqual(10, history.Count);
            Assert.AreEqual("Status 14", history[9].Status); // Should be the most recent
        }

        [TestMethod]
        public void FormDiscoveryMessages_ReturnCorrectTemplates()
        {
            // Test that all message templates return non-empty strings
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.Initializing));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.CheckingCache));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.ScanningForms));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.CheckingDatabase));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.ComparingForms));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.UpdatingCache));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.UsingCache));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.DiscoveryComplete));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.FormDiscoveryMessages.DiscoveryFailed));
        }

        [TestMethod]
        public void SyncMessages_ReturnCorrectTemplates()
        {
            // Test that all sync message templates return non-empty strings
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.CheckingConcurrent));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.Starting));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.InProgress));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.Locked));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.RefreshingData));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.Completed));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.Failed));
            Assert.IsFalse(string.IsNullOrEmpty(StatusProgressService.SyncMessages.Cancelled));
        }

        [TestMethod]
        public void FormDiscoveryMessages_MismatchDetected_ReturnsCorrectFormat()
        {
            // Arrange
            var missing = 3;
            var obsolete = 2;

            // Act
            var result = StatusProgressService.FormDiscoveryMessages.MismatchDetected(missing, obsolete);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("3 missing"));
            Assert.IsTrue(result.Contains("2 obsolete"));
            Assert.IsTrue(result.Contains("Mismatch detected"));
        }

        [TestMethod]
        public void FormDiscoveryMessages_FormsInSync_ReturnsCorrectFormat()
        {
            // Arrange
            var count = 15;

            // Act
            var result = StatusProgressService.FormDiscoveryMessages.FormsInSync(count);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("15 forms"));
            Assert.IsTrue(result.Contains("in sync"));
        }

        [TestMethod]
        public void StatusProgressService_HandlesNullInputsGracefully()
        {
            // Test null handling in various methods
            Assert.IsNotNull(StatusProgressService.FormatStatus(null, "test"));
            Assert.IsNotNull(StatusProgressService.FormatStatus("template", null));
            Assert.IsNotNull(StatusProgressService.FormatStatusWithCount(null, 1, 2));
            Assert.IsNotNull(StatusProgressService.FormatStatusWithPercentage(null, 50));
            Assert.IsNotNull(StatusProgressService.FormatStatusWithTiming(null, TimeSpan.FromMinutes(1)));
        }

        [TestMethod]
        public void StatusProgressService_HandlesInvalidInputsGracefully()
        {
            // Test invalid inputs
            Assert.AreEqual(0, StatusProgressService.CalculatePercentage(-1, 100));
            Assert.AreEqual(0, StatusProgressService.CalculatePercentage(50, -1));
            
            var timeRemaining = StatusProgressService.CalculateTimeRemaining(-1, 100, TimeSpan.FromMinutes(1));
            Assert.AreEqual(TimeSpan.Zero, timeRemaining);
        }

        [TestMethod]
        public void FormDiscoveryService_CompareFormsWithDatabase_HandlesNullProgress()
        {
            // This test might fail if there are database/file system issues
            // But it should at least not throw an exception
            try
            {
                var result = FormDiscoveryService.CompareFormsWithDatabase(null);
                Assert.IsNotNull(result);
            }
            catch (Exception ex)
            {
                // If it fails due to missing dependencies, that's expected in test environment
                Assert.IsTrue(ex.Message.Contains("directory") || ex.Message.Contains("database") || ex.Message.Contains("connection"));
            }
        }
    }
}
