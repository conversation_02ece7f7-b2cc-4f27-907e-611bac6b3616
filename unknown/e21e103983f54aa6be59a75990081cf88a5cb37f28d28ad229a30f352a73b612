using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ProManage.Modules.Services;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Verification tests to ensure background status implementation is working correctly
    /// </summary>
    [TestClass]
    public class BackgroundStatusVerification
    {
        [TestMethod]
        public void Verify_StatusProgressService_IsAccessible()
        {
            // Verify StatusProgressService can be accessed and used
            var message = StatusProgressService.FormDiscoveryMessages.Initializing;
            Assert.IsFalse(string.IsNullOrEmpty(message));
            Assert.IsTrue(message.Contains("form discovery"));
        }

        [TestMethod]
        public void Verify_FormDiscoveryService_AcceptsProgressReporter()
        {
            // Verify FormDiscoveryService accepts IProgress parameter
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => statusUpdates.Add(status));

            var result = FormDiscoveryService.CompareFormsWithDatabase(progress);
            
            Assert.IsNotNull(result);
            Assert.IsTrue(statusUpdates.Count > 0);
        }

        [TestMethod]
        public void Verify_StatusMessages_AreUserFriendly()
        {
            // Verify all status messages are user-friendly
            var messages = new[]
            {
                StatusProgressService.FormDiscoveryMessages.Initializing,
                StatusProgressService.FormDiscoveryMessages.ScanningForms,
                StatusProgressService.FormDiscoveryMessages.CheckingDatabase,
                StatusProgressService.SyncMessages.Starting,
                StatusProgressService.SyncMessages.Completed
            };

            foreach (var message in messages)
            {
                Assert.IsFalse(string.IsNullOrEmpty(message));
                Assert.IsTrue(message.Length > 5);
                // Should not contain technical jargon
                Assert.IsFalse(message.Contains("Exception"));
                Assert.IsFalse(message.Contains("null"));
            }
        }

        [TestMethod]
        public void Verify_StatusHistory_Works()
        {
            // Clear any existing history
            StatusProgressService.ClearHistory();
            
            // Add test entries
            StatusProgressService.AddToHistory("Test status 1", "Test operation");
            StatusProgressService.AddToHistory("Test status 2", "Test operation");
            
            var history = StatusProgressService.GetRecentHistory(5);
            
            Assert.AreEqual(2, history.Count);
            Assert.AreEqual("Test status 1", history[0].Status);
            Assert.AreEqual("Test status 2", history[1].Status);
        }

        [TestMethod]
        public async Task Verify_AsyncStatusReporting_Works()
        {
            var statusUpdates = new List<string>();
            IProgress<string> progress = new Progress<string>(status => statusUpdates.Add(status));

            await Task.Run(() =>
            {
                progress.Report("Async test started");
                Task.Delay(50).Wait();
                progress.Report("Async test completed");
            });

            // Allow time for progress updates
            await Task.Delay(100);

            Assert.IsTrue(statusUpdates.Count >= 2);
            Assert.IsTrue(statusUpdates.Contains("Async test started"));
            Assert.IsTrue(statusUpdates.Contains("Async test completed"));
        }

        [TestMethod]
        public void Verify_ErrorHandling_IsRobust()
        {
            // Test that methods handle null inputs gracefully
            Assert.IsNotNull(StatusProgressService.FormatStatus(null, "test"));
            Assert.IsNotNull(StatusProgressService.FormatStatus("template", null));
            
            // Test that FormDiscoveryService handles null progress
            var result = FormDiscoveryService.CompareFormsWithDatabase(null);
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void Verify_PerformanceIsAcceptable()
        {
            // Test that status operations are fast
            var startTime = DateTime.Now;
            
            for (int i = 0; i < 100; i++)
            {
                StatusProgressService.AddToHistory($"Performance test {i}", "Performance");
            }
            
            var elapsed = DateTime.Now - startTime;
            
            // Should complete 100 operations in under 100ms
            Assert.IsTrue(elapsed.TotalMilliseconds < 100);
        }

        [TestMethod]
        public void Verify_AllComponentsIntegrated()
        {
            // Verify all components work together
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => statusUpdates.Add(status));

            // This should exercise the full integration
            var result = FormDiscoveryService.CompareFormsWithDatabase(progress);
            
            Assert.IsNotNull(result);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Should have initialization message
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("Initializing") || s.Contains("cache")));
            
            // Should have completion message
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("complete") || s.Contains("sync")));
            
            // Verify status history was updated
            var history = StatusProgressService.GetRecentHistory(10);
            Assert.IsTrue(history.Count > 0);
        }
    }
}
