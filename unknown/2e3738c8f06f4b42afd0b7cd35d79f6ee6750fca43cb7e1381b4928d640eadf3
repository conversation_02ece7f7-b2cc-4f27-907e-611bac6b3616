using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Transactions;
using System.Threading.Tasks;
using Npgsql;
using Newtonsoft.Json.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for synchronizing forms with permission database tables
    /// Uses SERIALIZABLE isolation with configurable timeout
    /// </summary>
    public static class PermissionSyncService
    {
        /// <summary>
        /// Validate form name for security and format compliance with comprehensive SQL injection prevention
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <returns>True if valid</returns>
        public static bool ValidateFormName(string formName)
        {
            if (string.IsNullOrWhiteSpace(formName))
            {
                SyncLoggingService.LogSecurityEvent("FormNameValidation", "Null or empty form name rejected");
                return false;
            }

            // **CRITICAL**: Prevent SQL injection and path traversal
            var invalidChars = new char[] { '\'', '"', ';', '-', '/', '\\', '<', '>', '|', '&', '$', '`', '~', '!', '@', '#', '%', '^', '*', '(', ')', '[', ']', '{', '}', '=', '+' };

            if (formName.Any(c => invalidChars.Contains(c)))
            {
                SyncLoggingService.LogSecurityEvent("InvalidFormName", $"Rejected form name with invalid characters: {formName}");
                return false;
            }

            // Validate length
            if (formName.Length > 100)
            {
                SyncLoggingService.LogSecurityEvent("FormNameTooLong", $"Form name length: {formName.Length}");
                return false;
            }

            // Validate format (alphanumeric + underscore only)
            if (!System.Text.RegularExpressions.Regex.IsMatch(formName, @"^[a-zA-Z0-9_]+$"))
            {
                SyncLoggingService.LogSecurityEvent("InvalidFormNameFormat", $"Form name: {formName}");
                return false;
            }

            // Additional security checks for common injection patterns
            var lowerFormName = formName.ToLower();
            var suspiciousPatterns = new[] { "drop", "delete", "insert", "update", "select", "union", "exec", "script", "alert", "javascript" };

            if (suspiciousPatterns.Any(pattern => lowerFormName.Contains(pattern)))
            {
                SyncLoggingService.LogSecurityEvent("SuspiciousFormName", $"Form name contains suspicious pattern: {formName}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Add missing forms to both permission tables
        /// </summary>
        /// <param name="formNames">List of form names to add</param>
        /// <param name="progress">Progress reporter (optional)</param>
        /// <returns>True if all forms added successfully</returns>
        public static bool AddMissingFormsToPermissions(List<string> formNames, IProgress<SyncProgress> progress = null)
        {
            if (formNames == null || formNames.Count == 0)
                return true;

            var startTime = DateTime.Now;
            var totalOperations = formNames.Count * 2; // Each form goes to both tables
            var completed = 0;

            try
            {
                SyncLoggingService.LogSyncStart(formNames.Count);

                using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions
                    {
                        IsolationLevel = IsolationLevel.Serializable,
                        Timeout = GetConfigurableTimeout()
                    }))
                {
                    foreach (var formName in formNames)
                    {
                        ReportProgress(progress, completed, totalOperations, $"Adding form to role permissions: {formName}");

                        // Add to role_permissions for all roles
                        if (!AddFormToRolePermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"AddFormToRolePermissions({formName})", 
                                new Exception("Failed to add form to role permissions"));
                            return false;
                        }
                        completed++;

                        ReportProgress(progress, completed, totalOperations, $"Adding form to user permissions: {formName}");

                        // Add to user_permissions for all users
                        if (!AddFormToUserPermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"AddFormToUserPermissions({formName})", 
                                new Exception("Failed to add form to user permissions"));
                            return false;
                        }
                        completed++;
                    }

                    scope.Complete();
                }

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogSyncComplete(formNames.Count, 0, duration);
                return true;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("AddMissingFormsToPermissions", ex);
                return false;
            }
        }

        /// <summary>
        /// Remove obsolete forms from both permission tables
        /// </summary>
        /// <param name="formNames">List of form names to remove</param>
        /// <returns>True if all forms removed successfully</returns>
        public static bool RemoveObsoleteFormsFromPermissions(List<string> formNames)
        {
            if (formNames == null || formNames.Count == 0)
                return true;

            var startTime = DateTime.Now;

            try
            {
                SyncLoggingService.LogSyncStart(formNames.Count);

                using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions
                    {
                        IsolationLevel = IsolationLevel.Serializable,
                        Timeout = GetConfigurableTimeout()
                    }))
                {
                    foreach (var formName in formNames)
                    {
                        // Remove from role_permissions
                        if (!RemoveFormFromRolePermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"RemoveFormFromRolePermissions({formName})", 
                                new Exception("Failed to remove form from role permissions"));
                            return false;
                        }

                        // Remove from user_permissions
                        if (!RemoveFormFromUserPermissions(formName))
                        {
                            SyncLoggingService.LogSyncError($"RemoveFormFromUserPermissions({formName})", 
                                new Exception("Failed to remove form from user permissions"));
                            return false;
                        }
                    }

                    scope.Complete();
                }

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogSyncComplete(0, formNames.Count, duration);
                return true;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("RemoveObsoleteFormsFromPermissions", ex);
                return false;
            }
        }

        /// <summary>
        /// Execute full synchronization with progress reporting
        /// </summary>
        /// <param name="progress">Progress reporter (optional)</param>
        /// <param name="timeout">Custom timeout (optional)</param>
        /// <returns>Sync result with details</returns>
        public static FormSyncResult ExecuteFullSync(IProgress<SyncProgress> progress = null, TimeSpan? timeout = null)
        {
            var result = new FormSyncResult
            {
                SyncTimestamp = DateTime.Now
            };

            var startTime = DateTime.Now;

            try
            {
                // Get forms comparison
                var comparison = FormDiscoveryService.CompareFormsWithDatabase();
                if (comparison == null)
                {
                    result.Errors.Add("Failed to compare forms with database");
                    return result;
                }

                result.HasMismatch = comparison.HasMismatch;
                result.MissingForms = comparison.MissingForms;
                result.ObsoleteForms = comparison.ObsoleteForms;
                result.ExistingForms = comparison.ExistingForms;

                if (!comparison.HasMismatch)
                {
                    result.SyncSuccess = true;
                    return result;
                }

                // Add missing forms
                if (comparison.MissingForms.Count > 0)
                {
                    if (!AddMissingFormsToPermissions(comparison.MissingForms, progress))
                    {
                        result.Errors.Add("Failed to add missing forms");
                        return result;
                    }
                }

                // Remove obsolete forms
                if (comparison.ObsoleteForms.Count > 0)
                {
                    if (!RemoveObsoleteFormsFromPermissions(comparison.ObsoleteForms))
                    {
                        result.Errors.Add("Failed to remove obsolete forms");
                        return result;
                    }
                }

                result.SyncSuccess = true;
                result.TotalFormsProcessed = comparison.MissingForms.Count + comparison.ObsoleteForms.Count;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Sync failed: {ex.Message}");
                SyncLoggingService.LogSyncError("ExecuteFullSync", ex);
            }
            finally
            {
                result.SyncDuration = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// Validate permission tables integrity
        /// </summary>
        /// <returns>True if tables are valid</returns>
        public static bool ValidatePermissionTables()
        {
            try
            {
                // Check if tables exist and have expected structure
                var rolePermissionsCount = PermissionDatabaseService.GetAllRoles().Count;
                var userPermissionsCount = PermissionDatabaseService.GetAllUsers().Count;

                return rolePermissionsCount >= 0 && userPermissionsCount >= 0;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("ValidatePermissionTables", ex);
                return false;
            }
        }

        /// <summary>
        /// Report progress to UI thread
        /// </summary>
        /// <param name="progress">Progress reporter</param>
        /// <param name="completed">Completed operations</param>
        /// <param name="total">Total operations</param>
        /// <param name="operation">Current operation description</param>
        public static void ReportProgress(IProgress<SyncProgress> progress, int completed, int total, string operation)
        {
            if (progress == null) return;

            try
            {
                var syncProgress = new SyncProgress
                {
                    TotalOperations = total,
                    CompletedOperations = completed,
                    CurrentOperation = operation,
                    StartTime = DateTime.Now
                };

                progress.Report(syncProgress);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reporting progress: {ex.Message}");
            }
        }

        /// <summary>
        /// Get configurable timeout from app settings
        /// </summary>
        /// <returns>Timeout value</returns>
        public static TimeSpan GetConfigurableTimeout()
        {
            try
            {
                var timeoutMinutes = ConfigurationManager.AppSettings["FormSyncTimeoutMinutes"];
                if (int.TryParse(timeoutMinutes, out int minutes) && minutes > 0)
                {
                    return TimeSpan.FromMinutes(minutes);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reading timeout configuration: {ex.Message}");
            }

            // Default to 5 minutes
            return TimeSpan.FromMinutes(5);
        }

        /// <summary>
        /// Execute batch synchronization using SQL procedures with advisory locks
        /// Stage 1: Background synchronization with database
        /// </summary>
        /// <param name="formsToAdd">Forms to add to permission system</param>
        /// <param name="formsToRemove">Forms to remove from permission system</param>
        /// <param name="progress">Progress reporter (optional)</param>
        /// <returns>Sync result with detailed information</returns>
        public static async Task<FormSyncResult> ExecuteBatchSyncAsync(
            List<string> formsToAdd,
            List<string> formsToRemove,
            IProgress<SyncProgress> progress = null)
        {
            var result = new FormSyncResult
            {
                SyncTimestamp = DateTime.Now
            };

            var startTime = DateTime.Now;

            try
            {
                ReportProgress(progress, 0, 100, "Initializing batch sync operation...");

                // Validate input
                if ((formsToAdd == null || formsToAdd.Count == 0) &&
                    (formsToRemove == null || formsToRemove.Count == 0))
                {
                    result.SyncSuccess = true;
                    result.Errors.Add("No forms to sync");
                    return result;
                }

                ReportProgress(progress, 10, 100, "Connecting to database...");

                // Execute batch sync using SQL procedure
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    ReportProgress(progress, 20, 100, "Executing batch sync with advisory lock...");

                    using (var command = new NpgsqlCommand("SELECT sp_BatchSyncForms(@formsToAdd, @formsToRemove)", connection))
                    {
                        // Convert lists to PostgreSQL arrays
                        command.Parameters.AddWithValue("@formsToAdd",
                            formsToAdd?.ToArray() ?? new string[0]);
                        command.Parameters.AddWithValue("@formsToRemove",
                            formsToRemove?.ToArray() ?? new string[0]);

                        command.CommandTimeout = (int)GetConfigurableTimeout().TotalSeconds;

                        ReportProgress(progress, 50, 100, "Processing forms synchronization...");

                        var jsonResult = await command.ExecuteScalarAsync() as string;

                        if (string.IsNullOrEmpty(jsonResult))
                        {
                            result.Errors.Add("No result returned from batch sync procedure");
                            return result;
                        }

                        // Parse JSON result
                        var syncResult = JObject.Parse(jsonResult);

                        result.SyncSuccess = syncResult["success"]?.Value<bool>() ?? false;

                        if (result.SyncSuccess)
                        {
                            result.TotalFormsProcessed =
                                (syncResult["forms_added"]?.Value<int>() ?? 0) +
                                (syncResult["forms_removed"]?.Value<int>() ?? 0);
                            result.UsersAffected = syncResult["users_affected"]?.Value<int>() ?? 0;
                            result.RolesAffected = syncResult["roles_affected"]?.Value<int>() ?? 0;

                            // Update result lists
                            result.MissingForms = formsToAdd ?? new List<string>();
                            result.ObsoleteForms = formsToRemove ?? new List<string>();
                        }
                        else
                        {
                            var error = syncResult["error"]?.Value<string>() ?? "Unknown error";
                            result.Errors.Add($"Batch sync failed: {error}");
                        }
                    }
                }

                ReportProgress(progress, 90, 100, "Updating cache...");

                // Update cache after successful sync
                if (result.SyncSuccess)
                {
                    var allForms = FormDiscoveryService.GetFormsFromFileSystem();
                    FormScanCacheService.UpdateCache(allForms);
                }

                ReportProgress(progress, 100, 100, "Batch sync completed");

                var duration = DateTime.Now - startTime;
                SyncLoggingService.LogSyncComplete(
                    formsToAdd?.Count ?? 0,
                    formsToRemove?.Count ?? 0,
                    duration);
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Batch sync failed: {ex.Message}");
                SyncLoggingService.LogSyncError("ExecuteBatchSyncAsync", ex);
            }
            finally
            {
                result.SyncDuration = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// Get all form names from database using SQL procedure
        /// Stage 1: Database query for comparison
        /// </summary>
        /// <returns>List of normalized form names from database</returns>
        public static List<string> GetFormsFromDatabaseUsingProcedure()
        {
            try
            {
                var formNames = new List<string>();

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    using (var command = new NpgsqlCommand("SELECT * FROM sp_GetAllFormNamesFromPermissions()", connection))
                    {
                        command.CommandTimeout = 30;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var formName = reader["form_name"]?.ToString();
                                if (!string.IsNullOrEmpty(formName))
                                {
                                    formNames.Add(formName);
                                }
                            }
                        }
                    }
                }

                SyncLoggingService.LogFormDiscovery("GetFormsFromDatabaseUsingProcedure", formNames.Count, TimeSpan.Zero);
                return formNames.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("GetFormsFromDatabaseUsingProcedure", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// Validate database schema and create indexes if needed
        /// Stage 1: Database preparation
        /// </summary>
        /// <returns>True if validation successful</returns>
        public static bool ValidateAndOptimizeDatabase()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Create optimized indexes
                    using (var command = new NpgsqlCommand("SELECT sp_CreateOptimizedIndexes()", connection))
                    {
                        command.CommandTimeout = 120; // Allow time for index creation
                        command.ExecuteNonQuery();
                    }

                    // Validate form name casing
                    using (var command = new NpgsqlCommand("SELECT * FROM sp_ValidateFormNameCasing()", connection))
                    {
                        command.CommandTimeout = 30;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var tableName = reader["table_name"]?.ToString();
                                var duplicateCount = reader["duplicate_count"] as long? ?? 0;

                                if (duplicateCount > 0)
                                {
                                    SyncLoggingService.LogSyncError($"ValidateAndOptimizeDatabase",
                                        new Exception($"Found {duplicateCount} duplicate form names in {tableName}"));
                                    return false;
                                }
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("ValidateAndOptimizeDatabase", ex);
                return false;
            }
        }

        #region Private Helper Methods

        private static bool AddFormToRolePermissions(string formName)
        {
            try
            {
                var roles = PermissionDatabaseService.GetAllRoles();
                foreach (var role in roles)
                {
                    // Add form with default permissions (false for all)
                    PermissionDatabaseService.AddRolePermission(role.RoleId, formName, false, false, false, false, false);
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form to role permissions: {ex.Message}");
                return false;
            }
        }

        private static bool AddFormToUserPermissions(string formName)
        {
            try
            {
                var users = PermissionDatabaseService.GetAllUsers();
                foreach (var user in users)
                {
                    // Add form with null permissions (inherit from role)
                    PermissionDatabaseService.AddUserPermission(user.UserId, formName, null, null, null, null, null);
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form to user permissions: {ex.Message}");
                return false;
            }
        }

        private static bool RemoveFormFromRolePermissions(string formName)
        {
            try
            {
                PermissionDatabaseService.RemoveFormFromRolePermissions(formName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form from role permissions: {ex.Message}");
                return false;
            }
        }

        private static bool RemoveFormFromUserPermissions(string formName)
        {
            try
            {
                PermissionDatabaseService.RemoveFormFromUserPermissions(formName);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form from user permissions: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
