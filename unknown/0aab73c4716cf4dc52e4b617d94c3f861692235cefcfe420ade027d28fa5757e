-- =============================================
-- FIXED: Deploy Form Discovery SQL Procedures
-- Execute this script in your PostgreSQL database to fix the Form Discovery Service
-- =============================================

-- 1. Core procedure for getting all forms from both permission tables
CREATE OR REPLACE FUNCTION sp_GetAllFormNamesFromPermissions()
RETURNS TABLE(form_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT UPPER(up.form_name) as form_name
    FROM user_permissions up
    UNION
    SELECT DISTINCT UPPER(rp.form_name) as form_name
    FROM role_permissions rp
    ORDER BY form_name;
END;
$$ LANGUAGE plpgsql;

-- 2. Add form to all users with default permissions (FIXED VERSION)
CREATE OR REPLACE FUNCTION sp_AddFormToAllUsers(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    user_rec RECORD;
    insert_count INTEGER;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Add form to all existing users
    FOR user_rec IN SELECT user_id FROM users WHERE is_active = true LOOP
        INSERT INTO user_permissions (
            user_id, form_name, read_permission, new_permission, 
            edit_permission, delete_permission, print_permission,
            created_date
        ) VALUES (
            user_rec.user_id, p_form_name, false, false, 
            false, false, false,
            NOW()
        )
        ON CONFLICT (user_id, form_name) DO NOTHING;
        
        -- Get row count from the INSERT operation
        GET DIAGNOSTICS insert_count = ROW_COUNT;
        affected_rows := affected_rows + insert_count;
    END LOOP;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 3. Add form to all roles with default permissions (FIXED VERSION)
CREATE OR REPLACE FUNCTION sp_AddFormToAllRoles(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    role_rec RECORD;
    insert_count INTEGER;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Add form to all existing roles
    FOR role_rec IN SELECT role_id FROM roles WHERE is_active = true LOOP
        INSERT INTO role_permissions (
            role_id, form_name, read_permission, new_permission,
            edit_permission, delete_permission, print_permission,
            created_date
        ) VALUES (
            role_rec.role_id, p_form_name, false, false,
            false, false, false,
            NOW()
        )
        ON CONFLICT (role_id, form_name) DO NOTHING;
        
        -- Get row count from the INSERT operation
        GET DIAGNOSTICS insert_count = ROW_COUNT;
        affected_rows := affected_rows + insert_count;
    END LOOP;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 4. Remove form from both permission tables (FIXED VERSION)
CREATE OR REPLACE FUNCTION sp_RemoveFormFromPermissions(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    delete_count INTEGER;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Remove from user_permissions
    DELETE FROM user_permissions WHERE UPPER(form_name) = p_form_name;
    GET DIAGNOSTICS delete_count = ROW_COUNT;
    affected_rows := affected_rows + delete_count;
    
    -- Remove from role_permissions
    DELETE FROM role_permissions WHERE UPPER(form_name) = p_form_name;
    GET DIAGNOSTICS delete_count = ROW_COUNT;
    affected_rows := affected_rows + delete_count;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 5. Batch sync with advisory lock (SIMPLIFIED FIXED VERSION)
CREATE OR REPLACE FUNCTION sp_BatchSyncForms(
    p_forms_to_add TEXT[],
    p_forms_to_remove TEXT[]
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    forms_added INTEGER := 0;
    forms_removed INTEGER := 0;
    form_name TEXT;
    temp_count INTEGER;
BEGIN
    BEGIN
        -- Add missing forms
        IF p_forms_to_add IS NOT NULL THEN
            FOREACH form_name IN ARRAY p_forms_to_add LOOP
                -- Add to users and roles
                SELECT sp_AddFormToAllUsers(form_name) INTO temp_count;
                SELECT sp_AddFormToAllRoles(form_name) INTO temp_count;
                forms_added := forms_added + 1;
            END LOOP;
        END IF;

        -- Remove obsolete forms
        IF p_forms_to_remove IS NOT NULL THEN
            FOREACH form_name IN ARRAY p_forms_to_remove LOOP
                SELECT sp_RemoveFormFromPermissions(form_name) INTO temp_count;
                forms_removed := forms_removed + 1;
            END LOOP;
        END IF;

        -- Build success result
        result := json_build_object(
            'success', true,
            'forms_added', forms_added,
            'forms_removed', forms_removed,
            'timestamp', NOW()
        );

        RETURN result;

    EXCEPTION
        WHEN OTHERS THEN
            result := json_build_object(
                'success', false,
                'error', SQLERRM,
                'forms_added', 0,
                'forms_removed', 0
            );
            RETURN result;
    END;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Test the procedures after deployment:
-- SELECT * FROM sp_GetAllFormNamesFromPermissions();
-- SELECT sp_AddFormToAllUsers('TESTFORM');
-- SELECT sp_AddFormToAllRoles('TESTFORM');
-- SELECT sp_RemoveFormFromPermissions('TESTFORM');

-- =============================================
-- DEPLOYMENT INSTRUCTIONS
-- =============================================
-- 1. Connect to your PostgreSQL database
-- 2. Execute this entire script
-- 3. Verify procedures exist: SELECT proname FROM pg_proc WHERE proname LIKE 'sp_%form%';
-- 4. Test the Form Discovery Service in the application
