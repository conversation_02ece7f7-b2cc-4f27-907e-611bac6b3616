// Core CRUD operations and search functionality for EstimateForm data access
// Main repository for all database operations related to estimates

using System;
using System.Collections.Generic;
using System.Diagnostics;
using Npgsql;
using ProManage.Modules.Helpers;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Connections;
using ProManage.Modules.UI;

namespace ProManage.Modules.Data.EstimateForm
{
    /// <summary>
    /// Repository class for handling all database operations related to estimates
    /// Includes CRUD operations, search functionality, and navigation queries
    /// </summary>
    public static class EstimateFormRepository
    {
        #region Core CRUD Operations

        /// <summary>
        /// Gets an estimate by its ID
        /// </summary>
        /// <param name="id">The ID of the estimate to retrieve</param>
        /// <returns>EstimateFormHeaderModel object with details or null if not found</returns>
        public static EstimateFormHeaderModel GetEstimateById(int id)
        {
            Debug.WriteLine($"Repository: Getting estimate by ID: {id}");

            try
            {
                // Load SQL queries from files
                string headerQuery = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                                     SQLQueries.Estimate.GET_ESTIMATE_BY_ID,
                                                                     SQLQueries.Estimate.EstimateRetrieval.GET_BY_ID);

                string detailsQuery = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                                      SQLQueries.Estimate.GET_ESTIMATE_BY_ID,
                                                                      SQLQueries.Estimate.EstimateRetrieval.GET_DETAILS_BY_ID);

                EstimateFormHeaderModel estimate = null;

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    // Get header information
                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@estimate_id", id);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                estimate = MapEstimateFromReader(reader);
                            }
                        }
                    }

                    // Get details if header found
                    if (estimate != null)
                    {
                        Debug.WriteLine($"GetEstimateById: Loading details for estimate ID {estimate.Id}");
                        estimate.Details = GetEstimateDetailsById(estimate.Id);
                        Debug.WriteLine($"GetEstimateById: Loaded {estimate.Details?.Count ?? 0} details for estimate {estimate.EstimateNo}");
                    }
                }

                return estimate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR getting estimate by ID: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Saves an estimate (insert or update)
        /// </summary>
        /// <param name="estimate">The estimate to save</param>
        /// <returns>True if successful, False otherwise</returns>
        public static bool SaveEstimate(EstimateFormHeaderModel estimate)
        {
            Debug.WriteLine($"Repository: Saving estimate: {estimate.EstimateNo}");

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                bool isInsert = estimate.Id <= 0;

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            if (isInsert)
                            {
                                estimate.Id = InsertEstimateHeader(estimate, conn, transaction);
                            }
                            else
                            {
                                UpdateEstimateHeader(estimate, conn, transaction);
                            }

                            // Save details
                            SaveEstimateDetails(estimate, conn, transaction);

                            transaction.Commit();
                            Debug.WriteLine("Estimate saved successfully");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR saving estimate: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Re-throw the exception with more context so the UI can show the actual error
                throw new Exception($"Failed to save estimate: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Deletes an estimate by ID
        /// </summary>
        /// <param name="id">The ID of the estimate to delete</param>
        /// <returns>True if successful, False otherwise</returns>
        public static bool DeleteEstimate(int id)
        {
            Debug.WriteLine($"Repository: Deleting estimate ID: {id}");

            try
            {
                // Use EstimateDelete.sql with DeleteEstimate named query
                string deleteQuery = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                                     "EstimateDelete",
                                                                     "DeleteEstimate");

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            using (var cmd = new NpgsqlCommand(deleteQuery, conn, transaction))
                            {
                                cmd.Parameters.AddWithValue("@estimate_id", id);
                                int rowsAffected = cmd.ExecuteNonQuery();

                                if (rowsAffected > 0)
                                {
                                    transaction.Commit();
                                    Debug.WriteLine($"Estimate {id} deleted successfully");
                                    return true;
                                }
                                else
                                {
                                    transaction.Rollback();
                                    Debug.WriteLine($"No estimate found with ID {id}");
                                    return false;
                                }
                            }
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR deleting estimate: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Search Operations

        /// <summary>
        /// Gets all estimates from the database
        /// </summary>
        /// <returns>List of all EstimateFormHeaderModel objects</returns>
        public static List<EstimateFormHeaderModel> GetAllEstimates()
        {
            Debug.WriteLine("Repository: Getting all estimates");

            try
            {
                string headerQuery = SQLQueryLoader.LoadQuery(SQLQueries.Estimate.MODULE_NAME,
                                                             SQLQueries.Estimate.GET_ALL_ESTIMATES);

                var estimates = new List<EstimateFormHeaderModel>();

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                estimates.Add(estimate);
                            }
                        }
                    }

                    // Load details for each estimate (limit to prevent performance issues)
                    const int detailsLoadLimit = 100;
                    var estimatesToLoadDetails = estimates.Count <= detailsLoadLimit ?
                        estimates : estimates.GetRange(0, detailsLoadLimit);

                    foreach (var estimate in estimatesToLoadDetails)
                    {
                        estimate.Details = GetEstimateDetailsById(estimate.Id);
                    }
                }

                Debug.WriteLine($"Retrieved {estimates.Count} estimates");
                return estimates;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR getting all estimates: {ex.Message}");
                return new List<EstimateFormHeaderModel>();
            }
        }

        /// <summary>
        /// Gets an estimate by its estimate number
        /// </summary>
        /// <param name="estimateNo">The estimate number to search for</param>
        /// <returns>EstimateFormHeaderModel object or null if not found</returns>
        public static EstimateFormHeaderModel GetEstimateByNumber(string estimateNo)
        {
            Debug.WriteLine($"Getting estimate by number: {estimateNo}");

            try
            {
                string headerQuery = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                                     SQLQueries.Estimate.GET_ESTIMATE_BY_NUMBER,
                                                                     SQLQueries.Estimate.EstimateRetrieval.GET_BY_NUMBER);

                EstimateFormHeaderModel estimate = null;

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@estimate_number", estimateNo);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                estimate = MapEstimateFromReader(reader);
                            }
                        }
                    }

                    if (estimate != null)
                    {
                        estimate.Details = GetEstimateDetailsById(estimate.Id);
                    }
                }

                return estimate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR getting estimate by number: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Searches for estimates by customer name
        /// </summary>
        /// <param name="customerName">The customer name to search for (partial match)</param>
        /// <returns>List of matching EstimateFormHeaderModel objects</returns>
        public static List<EstimateFormHeaderModel> SearchEstimatesByCustomer(string customerName)
        {
            Debug.WriteLine($"Searching estimates by customer: {customerName}");

            try
            {
                string headerQuery = SQLQueryLoader.LoadQuery(SQLQueries.Estimate.MODULE_NAME,
                                                             SQLQueries.Estimate.SEARCH_ESTIMATES_BY_CUSTOMER);

                var estimates = new List<EstimateFormHeaderModel>();

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@search_term", $"%{customerName}%");
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                estimates.Add(estimate);
                            }
                        }
                    }

                    // Load details for each estimate
                    foreach (var estimate in estimates)
                    {
                        estimate.Details = GetEstimateDetailsById(estimate.Id);
                    }
                }

                Debug.WriteLine($"Found {estimates.Count} estimates for customer: {customerName}");
                return estimates;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR searching estimates by customer: {ex.Message}");
                return new List<EstimateFormHeaderModel>();
            }
        }

        #endregion





        #region Utility Methods

        /// <summary>
        /// Gets the next available estimate number in EST-YY-NNNNN format
        /// </summary>
        /// <returns>Next estimate number as string</returns>
        public static string GetNextEstimateNumber()
        {
            try
            {
                // Use EstimateUtilities.sql with GetNextEstimateNumber named query
                string query = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                               SQLQueries.Estimate.GET_NEXT_ESTIMATE_NUMBER,
                                                               "GetNextEstimateNumber");

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        var result = cmd.ExecuteScalar();

                        // If we have a result and it's not DBNull, return it
                        if (result != null && !Convert.IsDBNull(result))
                        {
                            return result.ToString();
                        }

                        // Fallback: Generate a number in the format EST-YY-NNNNN
                        string currentYear = DateTime.Now.Year.ToString().Substring(2);
                        return $"EST-{currentYear}-00001";
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR getting next estimate number: {ex.Message}");
                // Fallback: Generate a number in the format EST-YY-NNNNN with timestamp
                string currentYear = DateTime.Now.Year.ToString().Substring(2);
                string timeStamp = DateTime.Now.ToString("HHmm");
                return $"EST-{currentYear}-{timeStamp}1";
            }
        }

        /// <summary>
        /// Gets estimate details by estimate ID
        /// </summary>
        /// <param name="estimateId">The estimate ID</param>
        /// <returns>List of EstimateFormDetailModel objects</returns>
        public static List<EstimateFormDetailModel> GetEstimateDetailsById(int estimateId)
        {
            try
            {
                Debug.WriteLine($"GetEstimateDetailsById: Starting detail retrieval for estimate ID {estimateId}");

                string detailsQuery = SQLQueryLoader.ExtractNamedQuery(SQLQueries.Estimate.MODULE_NAME,
                                                                      SQLQueries.Estimate.GET_ESTIMATE_BY_ID,
                                                                      SQLQueries.Estimate.EstimateRetrieval.GET_DETAILS_BY_ID);

                var details = new List<EstimateFormDetailModel>();

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(detailsQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@estimate_id", estimateId);
                        using (var reader = cmd.ExecuteReader())
                        {
                            int rowCount = 0;
                            while (reader.Read())
                            {
                                var detail = MapEstimateDetailFromReader(reader);
                                details.Add(detail);
                                rowCount++;
                                Debug.WriteLine($"GetEstimateDetailsById: Loaded detail {rowCount}: {detail.PartNo} - {detail.Description}");
                            }
                        }
                    }
                }

                Debug.WriteLine($"GetEstimateDetailsById: Successfully retrieved {details.Count} details for estimate ID {estimateId}");
                return details;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR getting estimate details for ID {estimateId}: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return new List<EstimateFormDetailModel>();
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Maps an estimate from a data reader
        /// </summary>
        /// <param name="reader">The data reader</param>
        /// <returns>EstimateFormHeaderModel object</returns>
        private static EstimateFormHeaderModel MapEstimateFromReader(NpgsqlDataReader reader)
        {
            return new EstimateFormHeaderModel
            {
                // Handle both aliased and non-aliased column names for compatibility
                Id = Convert.ToInt32(HasColumn(reader, "estimate_id") ? reader["estimate_id"] : reader["id"]),
                EstimateNo = GetStringValue(reader, "estimate_number", "estimate_no"),
                CustomerName = GetStringValue(reader, "customer_name"),
                VIN = GetStringValue(reader, "vehicle_vin", "vin"),
                Brand = GetStringValue(reader, "vehicle_brand", "brand"),
                DocDate = GetDateTimeValue(reader, "estimate_date", "date"),
                Location = GetStringValue(reader, "location"),
                VehicleModel = GetStringValue(reader, "vehicle_model"),
                SalesmanName = GetStringValue(reader, "salesman_name"),
                Status = GetBooleanValue(reader, "status"),
                Remarks = GetStringValue(reader, "remarks"),
                CreatedAt = GetDateTimeValue(reader, "created_date", "created_at") ?? DateTime.Now,
                Details = new List<EstimateFormDetailModel>()
            };
        }

        /// <summary>
        /// Helper method to check if a column exists in the reader
        /// </summary>
        private static bool HasColumn(NpgsqlDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName) >= 0;
            }
            catch (IndexOutOfRangeException)
            {
                return false;
            }
        }

        /// <summary>
        /// Helper method to get string value from reader with fallback column names
        /// </summary>
        private static string GetStringValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? null : reader[columnName].ToString();
                }
            }
            return null;
        }

        /// <summary>
        /// Helper method to get DateTime value from reader with fallback column names
        /// </summary>
        private static DateTime? GetDateTimeValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader[columnName]);
                }
            }
            return null;
        }

        /// <summary>
        /// Helper method to get boolean value from reader
        /// </summary>
        private static bool GetBooleanValue(NpgsqlDataReader reader, string columnName)
        {
            if (HasColumn(reader, columnName))
            {
                return reader[columnName] != DBNull.Value && Convert.ToBoolean(reader[columnName]);
            }
            return false;
        }

        /// <summary>
        /// Helper method to get int value from reader with fallback column names
        /// </summary>
        private static int? GetIntValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? (int?)null : Convert.ToInt32(reader[columnName]);
                }
            }
            return null;
        }

        /// <summary>
        /// Helper method to get decimal value from reader with fallback column names
        /// </summary>
        private static decimal? GetDecimalValue(NpgsqlDataReader reader, params string[] columnNames)
        {
            foreach (var columnName in columnNames)
            {
                if (HasColumn(reader, columnName))
                {
                    return reader[columnName] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader[columnName]);
                }
            }
            return null;
        }

        /// <summary>
        /// Maps an estimate detail from a data reader
        /// </summary>
        /// <param name="reader">The data reader</param>
        /// <returns>EstimateFormDetailModel object</returns>
        private static EstimateFormDetailModel MapEstimateDetailFromReader(NpgsqlDataReader reader)
        {
            return new EstimateFormDetailModel
            {
                // Handle both aliased and non-aliased column names for compatibility
                Id = Convert.ToInt32(HasColumn(reader, "detail_id") ? reader["detail_id"] : reader["id"]),
                EstimateId = Convert.ToInt32(reader["estimate_id"]),
                PartNo = GetStringValue(reader, "part_number", "part_no"),
                Description = GetStringValue(reader, "description"),
                Qty = GetIntValue(reader, "quantity", "qty"),
                OEPrice = GetDecimalValue(reader, "oe_price"),
                AFMPrice = GetDecimalValue(reader, "afm_price"),
                Remarks = GetStringValue(reader, "remarks"),
                ApproveStatus = GetBooleanValue(reader, "approve_status")
            };
        }

        /// <summary>
        /// Inserts a new estimate header
        /// </summary>
        /// <param name="estimate">The estimate to insert</param>
        /// <param name="conn">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        /// <returns>The new estimate ID</returns>
        private static int InsertEstimateHeader(EstimateFormHeaderModel estimate, NpgsqlConnection conn, NpgsqlTransaction transaction)
        {
            string insertSql = SQLQueryLoader.ExtractNamedQuery(
                SQLQueries.Estimate.MODULE_NAME,
                SQLQueries.Estimate.ESTIMATE_CRUD,
                SQLQueries.Estimate.EstimateCRUD.INSERT_HEADER);

            using (var cmd = new NpgsqlCommand(insertSql, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@estimate_number", estimate.EstimateNo);
                cmd.Parameters.AddWithValue("@customer_name", estimate.CustomerName ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_vin", estimate.VIN ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_brand", estimate.Brand ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@estimate_date", estimate.DocDate ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@location", estimate.Location ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_model", estimate.VehicleModel ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@salesman_name", estimate.SalesmanName ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@status", estimate.Status);
                cmd.Parameters.AddWithValue("@remarks", estimate.Remarks ?? (object)DBNull.Value);

                var result = cmd.ExecuteScalar();
                return Convert.ToInt32(result);
            }
        }

        /// <summary>
        /// Updates an existing estimate header
        /// </summary>
        /// <param name="estimate">The estimate to update</param>
        /// <param name="conn">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        private static void UpdateEstimateHeader(EstimateFormHeaderModel estimate, NpgsqlConnection conn, NpgsqlTransaction transaction)
        {
            string updateSql = SQLQueryLoader.ExtractNamedQuery(
                SQLQueries.Estimate.MODULE_NAME,
                SQLQueries.Estimate.ESTIMATE_CRUD,
                SQLQueries.Estimate.EstimateCRUD.UPDATE_HEADER);

            using (var cmd = new NpgsqlCommand(updateSql, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@estimate_id", estimate.Id);
                cmd.Parameters.AddWithValue("@estimate_number", estimate.EstimateNo);
                cmd.Parameters.AddWithValue("@customer_name", estimate.CustomerName ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_vin", estimate.VIN ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_brand", estimate.Brand ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@estimate_date", estimate.DocDate ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@location", estimate.Location ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@vehicle_model", estimate.VehicleModel ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@salesman_name", estimate.SalesmanName ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@status", estimate.Status);
                cmd.Parameters.AddWithValue("@remarks", estimate.Remarks ?? (object)DBNull.Value);

                cmd.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// Saves estimate details
        /// </summary>
        /// <param name="estimate">The estimate with details to save</param>
        /// <param name="conn">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        private static void SaveEstimateDetails(EstimateFormHeaderModel estimate, NpgsqlConnection conn, NpgsqlTransaction transaction)
        {
            // Delete existing details
            string deleteSql = SQLQueryLoader.ExtractNamedQuery(
                SQLQueries.Estimate.MODULE_NAME,
                SQLQueries.Estimate.ESTIMATE_CRUD,
                SQLQueries.Estimate.EstimateCRUD.DELETE_ALL_DETAILS);

            using (var cmd = new NpgsqlCommand(deleteSql, conn, transaction))
            {
                cmd.Parameters.AddWithValue("@estimate_id", estimate.Id);
                cmd.ExecuteNonQuery();
            }

            // Insert new details
            if (estimate.Details != null && estimate.Details.Count > 0)
            {
                string insertSql = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    SQLQueries.Estimate.ESTIMATE_CRUD,
                    SQLQueries.Estimate.EstimateCRUD.INSERT_DETAIL);

                foreach (var detail in estimate.Details)
                {
                    using (var cmd = new NpgsqlCommand(insertSql, conn, transaction))
                    {
                        cmd.Parameters.AddWithValue("@estimate_id", estimate.Id);
                        cmd.Parameters.AddWithValue("@part_no", detail.PartNo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@description", detail.Description ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@qty", detail.Qty ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@oe_price", detail.OEPrice ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@afm_price", detail.AFMPrice ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@remarks", detail.Remarks ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@approve_status", detail.ApproveStatus ?? false);

                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        #endregion
    }
}
