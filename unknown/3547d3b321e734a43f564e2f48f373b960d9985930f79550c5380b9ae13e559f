using System;
using System.Data;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Net.Sockets;
using Npgsql;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Singleton class to manage a persistent database connection throughout the application.
    /// Provides centralized connection management, monitoring, and reconnection capabilities.
    ///
    /// This is the primary entry point for all database connections in the application.
    /// All components should use this manager to obtain database connections.
    /// </summary>
    public sealed class DatabaseConnectionManager
    {
        // Event for connection status changes
        public event EventHandler<bool> ConnectionStatusChanged;

        // Singleton instance
        private static DatabaseConnectionManager _instance;
        private static readonly object _lockObject = new object();

        // Connection objects
        private NpgsqlConnection _connection;
        private bool _isConnected = false;
        private string _lastError = string.Empty;

        // Connection monitoring timer
        private System.Windows.Forms.Timer _connectionMonitor;
        private bool _keepAliveEnabled = true; // Enable keep-alive by default

        // Reconnection attempt tracking
        private int _reconnectAttempts = 0;
        private int _maxReconnectAttempts = 5;
        private DateTime _lastConnectionAttempt = DateTime.MinValue;
        private int _reconnectBackoffSeconds = 30;  // Start with 30 seconds between attempts
        private int _maxReconnectBackoffSeconds = 300;  // Max 5 minutes between attempts

        // Connection pooling settings
        private const int MaxPoolSize = 100;
        private const int MinPoolSize = 1;
        private const int ConnectionLifetime = 300; // 5 minutes
        private const int ConnectionIdleLifetime = 60; // 1 minute

        /// <summary>
        /// Gets the singleton instance of the DatabaseConnectionManager
        /// </summary>
        public static DatabaseConnectionManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new DatabaseConnectionManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets the current database connection
        /// </summary>
        public NpgsqlConnection Connection
        {
            get { return _connection; }
        }

        /// <summary>
        /// Gets whether the database connection is currently open
        /// </summary>
        public bool IsConnected
        {
            get { return _isConnected; }
        }

        /// <summary>
        /// Gets whether the database connection is configured in the settings
        /// </summary>
        public bool IsConfigured
        {
            get
            {
                try
                {
                    // Check if connection string is configured
                    var connectionString = ConfigurationManager.ConnectionStrings["MyConnection"]?.ConnectionString;
                    return !string.IsNullOrEmpty(connectionString);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error checking if database is configured: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Gets the last error message
        /// </summary>
        public string LastError
        {
            get { return _lastError; }
        }

        /// <summary>
        /// Gets the current connection string for security validation
        /// </summary>
        /// <returns>Connection string or empty string if not configured</returns>
        public string GetConnectionString()
        {
            try
            {
                if (_connection != null && !string.IsNullOrEmpty(_connection.ConnectionString))
                {
                    return _connection.ConnectionString;
                }

                // Try to build connection string from settings
                var settings = ConfigurationHelper.LoadDatabaseSettings();
                if (settings.Count > 0 && settings.ContainsKey("Host"))
                {
                    string port = settings.ContainsKey("Port") ? settings["Port"] : "5432";
                    return string.Format(
                        "Host={0};Port={1};Database={2};Username={3};Password={4};" +
                        "Maximum Pool Size={5};Minimum Pool Size={6};" +
                        "Connection Lifetime={7};Connection Idle Lifetime={8};" +
                        "Timeout=15;Command Timeout=30;SslMode=Prefer;Trust Server Certificate=true;",
                        settings["Host"],
                        port,
                        settings["Database"],
                        settings["Username"],
                        settings["Password"],
                        MaxPoolSize, MinPoolSize, ConnectionLifetime, ConnectionIdleLifetime);
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting connection string: {ex.Message}");
                return string.Empty;
            }
        }

        // Private constructor to prevent direct instantiation
        private DatabaseConnectionManager()
        {
            // Initialize connection from app.config
            InitializeConnection();

            // Set up the connection monitor timer
            _connectionMonitor = new System.Windows.Forms.Timer();
            _connectionMonitor.Interval = 5000; // 5 seconds for more responsive connection monitoring
            _connectionMonitor.Enabled = false;
            _connectionMonitor.Tick += ConnectionMonitor_Tick;
        }

        /// <summary>
        /// Gets the current database connection.
        /// </summary>
        /// <returns>The current NpgsqlConnection object</returns>
        public NpgsqlConnection GetConnection()
        {
            return _connection;
        }

        /// <summary>
        /// Creates a new connection with the current settings.
        /// The connection is not opened automatically.
        /// </summary>
        /// <returns>A new NpgsqlConnection object (not opened)</returns>
        /// <exception cref="Exception">Thrown when connection settings are missing or invalid</exception>
        public NpgsqlConnection CreateNewConnection()
        {
            try
            {
                // Use ConfigurationHelper to load database settings
                var settings = ConfigurationHelper.LoadDatabaseSettings();

                // Check if settings were loaded
                if (settings.Count == 0 || !settings.ContainsKey("Host"))
                {
                    Debug.WriteLine("No database settings found for new connection");
                    throw new Exception("Database connection not configured. Please configure it in Settings.");
                }

                // Build connection string from settings
                string port = "5432"; // Default port
                if (settings.ContainsKey("Port"))
                {
                    port = settings["Port"];
                }

                string connString = string.Format(
                    "Host={0};Port={1};Database={2};Username={3};Password={4};" +
                    "Maximum Pool Size={5};Minimum Pool Size={6};" +
                    "Connection Lifetime={7};Connection Idle Lifetime={8};" +
                    "Timeout=15;Command Timeout=30;SslMode=Prefer;Trust Server Certificate=true;",
                    settings["Host"],
                    port,
                    settings["Database"],
                    settings["Username"],
                    settings["Password"],
                    MaxPoolSize, MinPoolSize, ConnectionLifetime, ConnectionIdleLifetime);

                Debug.WriteLine("Creating new connection with connection string: " + connString);

                // Create a new connection
                var newConnection = new NpgsqlConnection(connString);
                Debug.WriteLine("New NpgsqlConnection created successfully");

                return newConnection;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating new connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                throw new Exception($"Failed to create new connection: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Initializes the database connection from app.config or Development.config
        /// </summary>
        private void InitializeConnection()
        {
            try
            {
                // Use ConfigurationHelper to load database settings
                var settings = ConfigurationHelper.LoadDatabaseSettings();

                // Check if settings were loaded
                if (settings.Count == 0 || !settings.ContainsKey("Host"))
                {
                    Debug.WriteLine("No database settings found");
                    _isConnected = false;
                    _lastError = "Database connection not configured. Please configure it in Settings.";
                    _connection = null;
                    return;
                }

                // Build connection string from settings
                string port = "5432"; // Default port
                if (settings.ContainsKey("Port"))
                {
                    port = settings["Port"];
                }

                string connString = string.Format(
                    "Host={0};Port={1};Database={2};Username={3};Password={4};" +
                    "Maximum Pool Size={5};Minimum Pool Size={6};" +
                    "Connection Lifetime={7};Connection Idle Lifetime={8};" +
                    "Timeout=15;Command Timeout=30;SslMode=Prefer;Trust Server Certificate=true;",
                    settings["Host"],
                    port,
                    settings["Database"],
                    settings["Username"],
                    settings["Password"],
                    MaxPoolSize, MinPoolSize, ConnectionLifetime, ConnectionIdleLifetime);

                Debug.WriteLine("Built connection string from settings: " + connString);

                // Check if connection string exists and is not empty
                if (string.IsNullOrWhiteSpace(connString))
                {
                    Debug.WriteLine("Connection string is empty");
                    _isConnected = false;
                    _lastError = "Database connection not configured. Please configure it in Settings.";
                    _connection = null;
                    return;
                }

                // Create a new connection
                _connection = new NpgsqlConnection(connString);
                Debug.WriteLine("Created new NpgsqlConnection object");

                // Verify the connection string was set correctly
                if (string.IsNullOrEmpty(_connection.ConnectionString))
                {
                    Debug.WriteLine("Warning: Connection string not set properly in NpgsqlConnection");
                    _connection = null;
                    _lastError = "Invalid connection string format";
                }
                else
                {
                    Debug.WriteLine("Connection string set successfully in NpgsqlConnection");
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _lastError = ex.Message;
                _connection = null;
                Debug.WriteLine($"Error initializing connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
            }
        }

        /// <summary>
        /// Opens the database connection
        /// </summary>
        /// <returns>True if connection was successful, False otherwise</returns>
        public bool OpenConnection()
        {
            try
            {
                // Check if connection is already open
                if (_connection != null && _connection.State == ConnectionState.Open)
                {
                    // Reset reconnection attempts on successful connection
                    _reconnectAttempts = 0;
                    _isConnected = true;
                    Debug.WriteLine("Connection is already open");
                    return true;
                }

                // Check if we should delay reconnection attempt based on backoff strategy
                DateTime currentTime = DateTime.Now;
                TimeSpan timeSinceLastAttempt = currentTime - _lastConnectionAttempt;

                if (_reconnectAttempts > 0 && timeSinceLastAttempt.TotalSeconds < _reconnectBackoffSeconds)
                {
                    Debug.WriteLine($"Skipping connection attempt - in backoff period. Next attempt in {_reconnectBackoffSeconds - timeSinceLastAttempt.TotalSeconds:0.0} seconds");
                    return false;
                }

                // Update last attempt time
                _lastConnectionAttempt = currentTime;

                // Check if connection object exists
                if (_connection == null)
                {
                    Debug.WriteLine("Connection object is null, initializing...");
                    InitializeConnection();
                    if (_connection == null)
                    {
                        _isConnected = false;
                        _lastError = "Failed to initialize connection";
                        Debug.WriteLine("Failed to initialize connection");

                        // Increment reconnection attempts
                        _reconnectAttempts += 1;
                        UpdateReconnectBackoff();

                        return false;
                    }
                }

                // Check connection string
                if (string.IsNullOrEmpty(_connection.ConnectionString))
                {
                    _isConnected = false;
                    _lastError = "Connection string is empty. Please configure database settings.";
                    Debug.WriteLine("Connection string is empty");
                    return false;
                }

                Debug.WriteLine("Attempting to open connection with string: " + _connection.ConnectionString);

                // Open the connection
                _connection.Open();
                _isConnected = (_connection.State == ConnectionState.Open);

                Debug.WriteLine("Connection opened successfully: " + _isConnected);

                if (_isConnected)
                {
                    // Reset reconnection attempts on successful connection
                    _reconnectAttempts = 0;
                    _lastError = string.Empty;
                    // Raise the ConnectionStatusChanged event
                    ConnectionStatusChanged?.Invoke(this, true);
                }
                else
                {
                    _lastError = "Connection opened but state is not Open";
                    // Increment reconnection attempts
                    _reconnectAttempts += 1;
                    UpdateReconnectBackoff();
                }

                return _isConnected;
            }
            catch (SocketException ex) when (ex.Message.Contains("actively refused") || ex.Message.Contains("timed out"))
            {
                // Handle connection refused or timeout errors specifically
                _isConnected = false;
                _lastError = "Cannot connect to the database server. Please verify that:" + Environment.NewLine +
                             "1. The server is running and accessible" + Environment.NewLine +
                             "2. The hostname/IP address is correct" + Environment.NewLine +
                             "3. The port number is correct" + Environment.NewLine +
                             "4. Any firewall is configured to allow the connection" + Environment.NewLine +
                             "Technical details: " + ex.Message;
                Debug.WriteLine($"Socket error opening connection: {ex.Message}");

                // Increment reconnection attempts
                _reconnectAttempts += 1;
                UpdateReconnectBackoff();

                return false;
            }
            catch (NpgsqlException ex) when (ex.Message.Contains("password authentication failed"))
            {
                // Handle authentication errors specifically
                _isConnected = false;
                _lastError = "Database authentication failed. Please verify your username and password.";
                Debug.WriteLine($"Authentication error: {ex.Message}");

                // Don't increment reconnection attempts for auth failures - they won't resolve with time

                return false;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _lastError = ex.Message;
                Debug.WriteLine($"Error opening connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                    _lastError += Environment.NewLine + "Inner error: " + ex.InnerException.Message;
                }

                // Increment reconnection attempts
                _reconnectAttempts += 1;
                UpdateReconnectBackoff();

                return false;
            }
        }

        /// <summary>
        /// Updates the reconnection backoff time based on the number of attempts
        /// </summary>
        private void UpdateReconnectBackoff()
        {
            // Exponential backoff: double the time between attempts, up to a maximum
            _reconnectBackoffSeconds = Math.Min(
                _reconnectBackoffSeconds * 2,
                _maxReconnectBackoffSeconds);

            Debug.WriteLine($"Updated reconnect backoff to {_reconnectBackoffSeconds} seconds after {_reconnectAttempts} attempts");
        }

        /// <summary>
        /// Closes the database connection
        /// </summary>
        public void CloseConnection()
        {
            try
            {
                if (_connection != null && _connection.State == ConnectionState.Open)
                {
                    _connection.Close();
                    _isConnected = false;
                    Debug.WriteLine("Connection closed successfully");

                    // Raise the ConnectionStatusChanged event
                    ConnectionStatusChanged?.Invoke(this, false);
                }
            }
            catch (Exception ex)
            {
                _lastError = ex.Message;
                Debug.WriteLine($"Error closing connection: {ex.Message}");
            }
        }

        /// <summary>
        /// Tests the current database connection
        /// </summary>
        /// <returns>True if connection is successful, False otherwise</returns>
        public bool TestCurrentConnection()
        {
            try
            {
                // Check if connection is already open
                if (_connection != null && _connection.State == ConnectionState.Open)
                {
                    // Test with a simple query
                    using (var cmd = new NpgsqlCommand("SELECT 1", _connection))
                    {
                        cmd.CommandTimeout = 5; // Short timeout for test
                        var result = cmd.ExecuteScalar();
                        Debug.WriteLine($"Test query result: {result}");
                        return (result != null && Convert.ToInt32(result) == 1);
                    }
                }
                else
                {
                    // Try to open the connection
                    return OpenConnection();
                }
            }
            catch (Exception ex)
            {
                _lastError = ex.Message;
                Debug.WriteLine($"Error testing connection: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Tests a connection with the provided parameters
        /// </summary>
        /// <param name="server">Server address</param>
        /// <param name="port">Port number</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>True if connection is successful, False otherwise</returns>
        public bool TestConnection(string server, string port, string database, string username, string password, out string errorMessage)
        {
            NpgsqlConnection testConn = null;
            errorMessage = string.Empty;
            bool success = false;

            try
            {
                // Build the connection string
                string connString = string.Format(
                    "Host={0};Port={1};Database={2};Username={3};Password={4};" +
                    "Maximum Pool Size={5};Minimum Pool Size={6};" +
                    "Connection Lifetime={7};Connection Idle Lifetime={8};" +
                    "Timeout=15;Command Timeout=30;SslMode=Prefer;Trust Server Certificate=true;",
                    server, port, database, username, password,
                    MaxPoolSize, MinPoolSize, ConnectionLifetime, ConnectionIdleLifetime);

                Debug.WriteLine("Testing connection with string: " + connString);

                // Create a new connection
                testConn = new NpgsqlConnection(connString);
                testConn.Open();

                // Test with a simple query
                using (var cmd = new NpgsqlCommand("SELECT 1", testConn))
                {
                    cmd.CommandTimeout = 5; // Short timeout for test
                    var result = cmd.ExecuteScalar();
                    Debug.WriteLine($"Test query result: {result}");
                    success = (result != null && Convert.ToInt32(result) == 1);

                    if (success)
                    {
                        Debug.WriteLine("Test connection successful!");
                    }
                    else
                    {
                        Debug.WriteLine("Test query failed to return expected result");
                        errorMessage = "Test query failed to return expected result.";
                    }

                    return success;
                }
            }
            catch (SocketException ex) when (ex.Message.Contains("actively refused") || ex.Message.Contains("timed out"))
            {
                // Handle connection refused or timeout errors specifically
                errorMessage = "Cannot connect to the database server. Please verify that:" + Environment.NewLine +
                             "1. The server is running and accessible" + Environment.NewLine +
                             "2. The hostname/IP address is correct" + Environment.NewLine +
                             "3. The port number is correct" + Environment.NewLine +
                             "4. Any firewall is configured to allow the connection" + Environment.NewLine +
                             "Technical details: " + ex.Message;
                Debug.WriteLine($"Socket error testing connection: {ex.Message}");
                return false;
            }
            catch (NpgsqlException ex) when (ex.Message.Contains("password authentication failed"))
            {
                // Handle authentication errors specifically
                errorMessage = "Database authentication failed. Please verify your username and password.";
                Debug.WriteLine($"Authentication error: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + "Inner error: " + ex.InnerException.Message;
                }
                Debug.WriteLine($"Error testing connection: {ex.Message}");
                return false;
            }
            finally
            {
                // Close and dispose the test connection
                if (testConn != null)
                {
                    if (testConn.State == ConnectionState.Open)
                    {
                        testConn.Close();
                    }
                    testConn.Dispose();
                    Debug.WriteLine($"Test connection closed and disposed. Success = {success}");
                }
            }
        }

        /// <summary>
        /// Updates the database connection with new settings
        /// </summary>
        /// <param name="server">Server address</param>
        /// <param name="port">Port number</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>True if connection was successful, False otherwise</returns>
        public bool UpdateConnection(string server, string port, string database, string username, string password)
        {
            try
            {
                // Close the existing connection if it's open
                CloseConnection();

                // Force disposal of existing connection to ensure clean state
                if (_connection != null)
                {
                    _connection.Dispose();
                    _connection = null;
                }

                // Build the connection string
                string connString = string.Format(
                    "Host={0};Port={1};Database={2};Username={3};Password={4};" +
                    "Maximum Pool Size={5};Minimum Pool Size={6};" +
                    "Connection Lifetime={7};Connection Idle Lifetime={8};" +
                    "Timeout=15;Command Timeout=30;SslMode=Prefer;Trust Server Certificate=true;",
                    server, port, database, username, password,
                    MaxPoolSize, MinPoolSize, ConnectionLifetime, ConnectionIdleLifetime);

                Debug.WriteLine("New connection string: " + connString);

                // Create a new connection
                _connection = new NpgsqlConnection(connString);
                Debug.WriteLine("Created new NpgsqlConnection with updated settings");

                // Update the connection string in app.config for persistence
                try
                {
                    // Use ConfigurationHelper to save database settings
                    var saveResult = ConfigurationHelper.SaveDatabaseSettings(
                        server,
                        port,
                        database,
                        username,
                        password);

                    if (saveResult)
                    {
                        Debug.WriteLine("Saved connection string to app.config using ConfigurationHelper");
                    }
                    else
                    {
                        Debug.WriteLine("Failed to save connection string using ConfigurationHelper");
                        _lastError = "Failed to save connection string to configuration";
                        return false;
                    }
                }
                catch (Exception configEx)
                {
                    Debug.WriteLine($"Error updating app.config: {configEx.Message}");
                    _lastError = $"Error saving configuration: {configEx.Message}";
                    return false;
                    // Don't continue if config update fails - this ensures we don't get into an inconsistent state
                }

                // Explicitly reset connection status variables
                _isConnected = false;
                _reconnectAttempts = 0;
                _reconnectBackoffSeconds = 30;
                _lastConnectionAttempt = DateTime.MinValue;

                // Try to open the connection with a new instance to ensure it's using the updated settings
                bool result = OpenConnection();
                Debug.WriteLine("Connection open result: " + result);

                // Start connection monitoring if connection was successful
                if (result)
                {
                    StartConnectionMonitoring();

                    // Preload SQL queries
                    PreloadSQLQueries();

                    // Raise the ConnectionStatusChanged event to notify listeners
                    ConnectionStatusChanged?.Invoke(this, true);
                }

                return result;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _lastError = ex.Message;
                Debug.WriteLine($"Error updating connection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                    _lastError += Environment.NewLine + "Inner error: " + ex.InnerException.Message;
                }
                return false;
            }
        }

        /// <summary>
        /// Starts the connection monitoring timer to maintain a persistent connection
        /// </summary>
        public void StartConnectionMonitoring()
        {
            // Reset reconnection attempts when explicitly starting monitoring
            _reconnectAttempts = 0;
            _reconnectBackoffSeconds = 30;  // Reset to initial backoff time
            _lastConnectionAttempt = DateTime.MinValue;  // Reset last attempt time

            _keepAliveEnabled = true;
            _connectionMonitor.Enabled = true;
            Debug.WriteLine("Connection monitoring started with reset reconnection parameters");
        }

        /// <summary>
        /// Stops the connection monitoring timer
        /// </summary>
        public void StopConnectionMonitoring()
        {
            _keepAliveEnabled = false;
            _connectionMonitor.Enabled = false;
            Debug.WriteLine("Connection monitoring stopped");
        }

        /// <summary>
        /// Timer event handler to periodically check and maintain the database connection
        /// </summary>
        private void ConnectionMonitor_Tick(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Connection monitor checking connection status...");

                // Check if the connection is still open
                if (_connection != null)
                {
                    // Check if connection is closed or broken
                    if (_connection.State != ConnectionState.Open)
                    {
                        Debug.WriteLine("Connection is closed or broken");

                        bool wasConnected = _isConnected;
                        _isConnected = false;

                        // Raise the ConnectionStatusChanged event if the status actually changed
                        if (wasConnected)
                        {
                            ConnectionStatusChanged?.Invoke(this, false);
                        }

                        // Check if we should attempt reconnection based on backoff strategy
                        DateTime currentTime = DateTime.Now;
                        TimeSpan timeSinceLastAttempt = currentTime - _lastConnectionAttempt;

                        if (_reconnectAttempts < _maxReconnectAttempts ||
                           timeSinceLastAttempt.TotalSeconds >= _reconnectBackoffSeconds)
                        {
                            Debug.WriteLine("Attempting to reconnect...");
                            // Try to reopen the connection
                            OpenConnection();
                        }
                        else
                        {
                            Debug.WriteLine($"Skipping reconnection attempt - in backoff period. Next attempt in {_reconnectBackoffSeconds - timeSinceLastAttempt.TotalSeconds:0.0} seconds");
                        }
                    }
                    else
                    {
                        // Connection is open, update status if it was previously closed
                        bool wasConnected = _isConnected;
                        _isConnected = true;

                        // Raise the ConnectionStatusChanged event if the status actually changed
                        if (!wasConnected)
                        {
                            ConnectionStatusChanged?.Invoke(this, true);
                        }

                        Debug.WriteLine("Connection is still open");

                        // Optional: Execute a simple query to keep the connection alive
                        if (_keepAliveEnabled)
                        {
                            try
                            {
                                using (var cmd = new NpgsqlCommand("SELECT 1", _connection))
                                {
                                    cmd.CommandTimeout = 5;  // Short timeout for keep-alive
                                    cmd.ExecuteScalar();
                                    Debug.WriteLine("Keep-alive query executed successfully");
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"Error executing keep-alive query: {ex.Message}");
                                // Mark as disconnected but don't try to reconnect immediately
                                // The next tick will handle reconnection with backoff
                                _isConnected = false;
                            }
                        }
                    }
                }
                else
                {
                    // Connection object is null, try to initialize it
                    Debug.WriteLine("Connection object is null, initializing...");
                    InitializeConnection();
                    if (_connection != null)
                    {
                        // Try to open the connection
                        OpenConnection();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in connection monitor: {ex.Message}");
                // Don't throw from timer event handler
            }
        }

        /// <summary>
        /// Preloads SQL queries from the Procedures folder for faster access
        /// </summary>
        private void PreloadSQLQueries()
        {
            try
            {
                Debug.WriteLine("Preloading SQL queries...");

                // Preload Estimate module queries
                SQLQueryLoader.PreloadModuleQueries("Estimate");

                // Preload SQLQuery module queries
                SQLQueryLoader.PreloadModuleQueries("SQLQuery");

                // Preload other modules as needed
                // SQLQueryLoader.PreloadModuleQueries("Customer");
                // SQLQueryLoader.PreloadModuleQueries("Product");

                Debug.WriteLine("SQL queries preloaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading SQL queries: {ex.Message}");
                // Don't throw - this is not critical
            }
        }

        /// <summary>
        /// Refreshes the database connection by closing and reinitializing it
        /// </summary>
        public void RefreshConnection()
        {
            Debug.WriteLine("RefreshConnection called");
            try
            {
                // Close the existing connection if it's open
                if (_connection != null)
                {
                    if (_connection.State != ConnectionState.Closed)
                    {
                        Debug.WriteLine("Closing existing connection...");
                        _connection.Close();
                    }
                    Debug.WriteLine("Disposing existing connection...");
                    _connection.Dispose();
                    _connection = null;
                }

                // Reset connection status
                _isConnected = false;

                // Call InitializeConnection to create a new connection
                Debug.WriteLine("Reinitializing connection...");
                InitializeConnection();

                // Notify listeners of connection status change
                ConnectionStatusChanged?.Invoke(this, _isConnected);

                Debug.WriteLine($"Connection refreshed. New status: {(_isConnected ? "Connected" : "Disconnected")}");
            }
            catch (Exception ex)
            {
                _lastError = $"Error refreshing connection: {ex.Message}";
                Debug.WriteLine($"Error in RefreshConnection: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                // Notify listeners of connection status change
                ConnectionStatusChanged?.Invoke(this, false);
                throw;
            }
        }
    }
}
