# ProManage Ribbon Button Logic

This document outlines the comprehensive ribbon button logic patterns used throughout the ProManage application, implemented via the centralized MenuRibbon User Control.

## Core Button States

### Edit Mode vs Ready Mode
- **Ready Mode** (`IsEditMode = false`): User can start operations (New/Edit/Delete enabled)
- **Edit Mode** (`IsEditMode = true`): User is actively editing (Save/Cancel enabled)

### Permission-Based Control
All buttons respect a two-level permission system:
1. **Global Permissions**: First-level filter (stored in users table)
2. **Form-Specific Permissions**: Second-level filter (role/user permissions)

## List Form Ribbon Button Logic

### Basic Operations

#### New Button
- **Enabled When**: `canCreate && !isEditMode && systemReady`
- **Disabled When**: `!canCreate || isEditMode || systemError`
  - Edit button clicked (form in edit mode)
  - User lacks create permission
  - System database error
- **Function**: Opens new entry form or creates new record

#### Edit Button
- **Enabled When**: `canEdit && !isEditMode && hasSelection && recordValid`
- **Disabled When**: `!canEdit || isEditMode || !hasSelection || recordInvalid`
  - New button clicked (form in new mode)
  - No row selected in grid
  - User lacks edit permission
  - Record is locked by another user
- **Function**: Opens selected record for editing

#### Delete Button
- **Enabled When**: `canDelete && !isEditMode && hasSelection && canDeleteRecord`
- **Disabled When**: `!canDelete || isEditMode || !hasSelection || hasDependent`
  - Edit/New button clicked (form in edit mode)
  - No row selected in grid
  - Record has dependent data
  - User lacks delete permission
- **Function**: Deletes selected record after confirmation

#### Save Button
- **Enabled When**: `isEditMode && hasUnsavedChanges && validationPassed`
- **Disabled When**: `!isEditMode || !hasUnsavedChanges || validationFailed`
  - Form in ready mode (no editing active)
  - No changes made to data
  - Required fields are empty
  - Validation errors exist
- **Function**: Saves current changes and returns to ready mode

#### Cancel Button
- **Enabled When**: `isEditMode && !savingInProgress`
- **Disabled When**: `!isEditMode || savingInProgress`
  - Form in ready mode (no editing active)
  - Save operation in progress
  - No edit session to cancel
- **Function**: Discards unsaved changes and returns to ready mode

### Navigation Buttons

#### First/Previous/Next/Last Buttons
- **Enabled When**: `canRead && !isEditMode && hasRecords`
- **Disabled When**: `!canRead || isEditMode || !hasRecords`
  - Edit/New button clicked (prevents data loss)
  - User lacks read permission
  - No records exist to navigate
  - Database connection lost
- **Function**: Navigate between records while preserving current state

### Print Operations

#### Print/Print Preview Buttons
- **Enabled When**: `canPrint && hasData && printSystemReady`
- **Disabled When**: `!canPrint || !hasData || printSystemError`
  - User lacks print permission
  - No data loaded to print
  - Print system unavailable
  - Report templates missing
- **Function**: Generate printed output or preview of current data

### Grid Operations

#### Add Row Button (Entry Forms Only)
- **Enabled When**: `canEdit && isEditMode && gridAllowsEditing`
- **Disabled When**: `!canEdit || !isEditMode || gridReadOnly`
  - Form in ready mode (not editing)
  - User lacks edit permission
  - Grid is read-only
  - Maximum rows reached
- **Function**: Adds new row to grid for new data entry in entry forms

#### Status Toggle Button (Special Entry Forms Only)
- **Enabled When**: `canEdit && hasData && statusChangeable`
- **Disabled When**: `!canEdit || !hasData || statusLocked`
  - User lacks edit permission
  - No record loaded
  - Data is already locked
  - Business rules prevent locking/unlocking
- **Function**: Locks/unlocks data to prevent editing (not available in all forms)

## Entry Form Ribbon Button Logic

### Standard Entry Forms (UserMaster, RoleMaster, etc.)

#### Ready State (View Mode)
- **New Button**:
  - Enabled: `canCreate && !isEditMode && systemReady`
  - Disabled: `!canCreate || isEditMode || systemError`
- **Edit Button**:
  - Enabled: `canEdit && !isEditMode && hasData && recordValid`
  - Disabled: `!canEdit || isEditMode || !hasData || recordInvalid`
- **Delete Button**:
  - Enabled: `canDelete && !isEditMode && hasData && canDeleteRecord`
  - Disabled: `!canDelete || isEditMode || !hasData || hasDependent`
- **Save Button**:
  - Enabled: `false` (always disabled in ready mode)
  - Disabled: `true` (no edit operation active)
- **Cancel Button**:
  - Enabled: `false` (always disabled in ready mode)
  - Disabled: `true` (no edit operation to cancel)
- **Navigation Buttons**:
  - Enabled: `canRead && !isEditMode && hasRecords`
  - Disabled: `!canRead || isEditMode || !hasRecords`
- **Print Buttons**:
  - Enabled: `canPrint && hasData && printSystemReady`
  - Disabled: `!canPrint || !hasData || printSystemError`
- **Grid Operations** (Entry Forms Only):
  - Enabled: `false` (not in edit mode)
  - Disabled: `true` (grid editing requires edit mode)
- **Status Toggle** (Special Forms Only):
  - Enabled: `canEdit && hasData && statusChangeable`
  - Disabled: `!canEdit || !hasData || statusLocked`

#### New Mode
- **New Button**:
  - Enabled: `false` (already in new mode)
  - Disabled: `true` (prevent multiple new operations)
- **Edit Button**:
  - Enabled: `false` (in edit mode)
  - Disabled: `true` (cannot edit while creating new)
- **Delete Button**:
  - Enabled: `false` (in edit mode, no record to delete)
  - Disabled: `true` (cannot delete unsaved record)
- **Save Button**:
  - Enabled: `isEditMode && hasUnsavedChanges && validationPassed`
  - Disabled: `!hasUnsavedChanges || validationFailed || systemError`
- **Cancel Button**:
  - Enabled: `isEditMode && !savingInProgress`
  - Disabled: `!isEditMode || savingInProgress`
- **Navigation Buttons**:
  - Enabled: `false` (prevent data loss)
  - Disabled: `true` (unsaved changes would be lost)
- **Print Buttons**:
  - Enabled: `false` (no saved data to print)
  - Disabled: `true` (cannot print unsaved record)
- **Grid Operations** (Entry Forms Only):
  - Enabled: `canEdit && isEditMode && gridAllowsEditing`
  - Disabled: `!canEdit || !isEditMode || gridReadOnly`
- **Status Toggle** (Special Forms Only):
  - Enabled: `canEdit && isEditMode && statusApplicable`
  - Disabled: `!canEdit || !isEditMode || statusNotApplicable`

#### Edit Mode
- **New Button**:
  - Enabled: `false` (in edit mode)
  - Disabled: `true` (cannot start new while editing)
- **Edit Button**:
  - Enabled: `false` (already in edit mode)
  - Disabled: `true` (already editing current record)
- **Delete Button**:
  - Enabled: `false` (in edit mode)
  - Disabled: `true` (cannot delete while editing)
- **Save Button**:
  - Enabled: `isEditMode && hasUnsavedChanges && validationPassed`
  - Disabled: `!hasUnsavedChanges || validationFailed || systemError`
- **Cancel Button**:
  - Enabled: `isEditMode && !savingInProgress`
  - Disabled: `!isEditMode || savingInProgress`
- **Navigation Buttons**:
  - Enabled: `false` (prevent data loss)
  - Disabled: `true` (unsaved changes would be lost)
- **Print Buttons**:
  - Enabled: `canPrint && hasOriginalData && printSystemReady`
  - Disabled: `!canPrint || !hasOriginalData || printSystemError`
- **Grid Operations** (Entry Forms Only):
  - Enabled: `canEdit && isEditMode && gridAllowsEditing`
  - Disabled: `!canEdit || !isEditMode || gridReadOnly`
- **Status Toggle** (Special Forms Only):
  - Enabled: `canEdit && isEditMode && statusChangeable`
  - Disabled: `!canEdit || !isEditMode || statusLocked`

### Special Form Configurations

#### Database Form
- All operation buttons hidden
- Only basic form functionality available

#### SQL Query Form
- Operation buttons hidden
- Print buttons available for query results
- Navigation buttons hidden

#### Permission Management Form
- Operation buttons visible
- Navigation buttons hidden (single-purpose form)
- Grid operations handled by form logic

## Button State Transition Logic

### State Transitions
1. **Ready → New Mode**:
   - New button clicked
   - Clear form, enable fields
   - Show Save/Cancel, hide others

2. **Ready → Edit Mode**:
   - Edit button clicked
   - Load data, enable fields
   - Show Save/Cancel, hide others

3. **Edit/New → Ready**:
   - Save or Cancel clicked
   - Disable fields, refresh data
   - Show New/Edit/Delete, hide Save/Cancel

### Validation Rules
- Save button only enabled when form has unsaved changes
- Navigation disabled during edit to prevent data loss
- Delete requires confirmation dialog
- Edit/Delete require data to be loaded

## Permission Integration

### Global Permission Checks
```csharp
bool canCreate = HasEffectivePermission("new");
bool canEdit = HasEffectivePermission("edit");
bool canDelete = HasEffectivePermission("delete");
bool canPrint = HasEffectivePermission("print");
bool canRead = HasEffectivePermission("read");
```

### Effective Permission Logic
1. Check global permission first
2. If global denied, deny access
3. If global allowed, check form-specific permission
4. Operational forms bypass permission checks

### Operational Forms
Forms that bypass strict permission control:
- Utility forms (PrintPreviewForm)
- Informational dialogs (AboutBox)
- Parameter entry utilities
- Non-business critical forms

## Implementation Pattern

### MenuRibbon UC Integration
```csharp
// Configure for form type
menuRibbon.ConfigureForFormType("usermaster");
menuRibbon.FormName = "UserMasterForm";
menuRibbon.CurrentUserId = currentUserId;

// Update states
menuRibbon.IsEditMode = isInEditMode;
menuRibbon.HasUnsavedChanges = isDirty;
menuRibbon.RefreshAll();
```

### Event Handling
```csharp
// Wire up events
menuRibbon.NewClicked += btnNew_ItemClick;
menuRibbon.EditClicked += btnEdit_ItemClick;
menuRibbon.SaveClicked += btnSave_ItemClick;
menuRibbon.CancelClicked += btnCancel_ItemClick;
menuRibbon.DeleteClicked += btnDelete_ItemClick;
```

## Error Handling

### Safety Measures
- **Permission Errors**: All buttons disabled when permission check fails
- **Exception Handling**: Default to deny access on any exceptions
- **Design Mode**: All permission checks bypassed (all buttons enabled)
- **Invalid User IDs**: All buttons disabled when userId <= 0

### Fallback Behavior
- **Unknown Form Types**: Use default configuration (all button groups visible)
- **Missing Permissions**: Default to false (buttons disabled)
- **Database Errors**: All buttons disabled for safety
- **System Errors**: All buttons disabled when system errors occur

### Button Disable Scenarios
- **No Permission**: Button disabled if user lacks required permission
- **Wrong Mode**: Operation buttons disabled in edit mode, Save/Cancel disabled in ready mode
- **No Data**: Edit/Delete disabled when no record is loaded
- **No Selection**: Edit/Delete disabled in list forms when no row selected
- **No Changes**: Save button disabled when no unsaved changes exist