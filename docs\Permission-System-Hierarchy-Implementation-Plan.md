# ProManage Permission System Hierarchy Implementation Plan

## Overview

This plan implements the corrected ProManage permission system hierarchy with MenuRibbon UC integration. The system calculates final permissions for each user and feeds them to MenuRibbon UC controls on every form.

## Current Database Analysis

**Users Table Structure (PostgreSQL):**
- Global Permission Fields: `new_perm`, `edit_perm`, `delete_perm`, `print_perm` (boolean, default false)
- Override Field: `user_perm_overrides` (boolean, default false) - **ALREADY EXISTS**

**Key Finding:** Database structure is complete. Global permissions stored in users table, not separate global_permissions table.

## Correct Permission Hierarchy

**Permission System Architecture:**
1. **Global Permissions** (Master Control) - Controls New/Edit/Delete/Print buttons across all forms
2. **Role Permissions** - Default form-specific permissions including Read (form visibility)
3. **User Permission Override** - Only applies when user_perm_overrides flag is TRUE

**Key Rules:**
- Global permissions control button availability system-wide (New/Edit/Delete/Print)
- Read permission in role/user tables controls form visibility to specific roles/users
- User permissions editable only when user_perm_overrides = TRUE
- MenuRibbon UC added to every form controls all ribbon buttons
- Final calculated permissions feed into MenuRibbon UC logic
- No real-time updates - changes take effect on application restart

## Implementation Phases

### Phase 1: Database Service Updates

#### 1.1 Update Database Service
**File:** `Modules/Connections/PermissionDatabaseService.cs`
- Update queries to use users table fields (new_perm, edit_perm, delete_perm, print_perm)
- Remove references to global_permissions table
- Handle existing user_perm_overrides field properly

### Phase 2: UI Implementation

#### 2.1 User Entry Form Updates
**Target Form:** Need to identify correct User Entry/Add User form
- Permission tab already exists.
- `chkUserPermOverrides` checkbox (editable) already exists.
- Link checkbox to existing `user_perm_overrides` field in users table
- Add proper data binding and validation
- Update save/load procedures to handle override flag

#### 2.2 Permission Management Form Updates
**File:** `Forms/MainForms/PermissionManagementForm.cs`

- `chkPermissionOverride` checkbox (read-only) in User Permissions tab already exists.
- User permissions grid editable only when user_perm_overrides = TRUE
- Display override status and visual indicators for inherited vs overridden permissions
- Remove global_permissions table references

### Phase 3: MenuRibbon UC Integration

#### 3.1 MenuRibbon UC Final Permission Logic
**File:** `Forms/ReusableForms/MenuRibbon.cs`
- Implement HasEffectivePermission method for final permission calculation:
  - Check Global Permission from users table (new_perm, edit_perm, delete_perm, print_perm)
  - If Global Permission FALSE: return FALSE
  - If Global Permission TRUE: Check form-specific permission based on user_perm_overrides flag
  - Return final calculated permission (Global AND Form-Specific)
- Integrate with existing ribbon button logic from RibbonButtonLogic.md
- Control New/Edit/Delete/Print buttons based on final permissions and form state

#### 3.2 Permission Service Core Logic
**File:** `Modules/Services/PermissionService.cs`
- Update permission resolution algorithm to support MenuRibbon UC
- Remove global_permissions table references
- Provide methods for MenuRibbon UC to query final permissions

#### 3.3 Permission Models Update
**File:** `Modules/Models/PermissionManagementForm/PermissionModels.cs`
- Update models for users table structure
- Add UserPermOverrides property
- Add global permission properties (new_perm, edit_perm, delete_perm, print_perm)
- Remove global_permissions table references

### Phase 4: Algorithm Testing

#### 4.1 MenuRibbon UC Algorithm Testing
**Test MenuRibbon UC functionality:**
- Test HasEffectivePermission method with different user scenarios
- Verify permission hierarchy logic works correctly
- Test button control logic responds to calculated permissions
- Validate integration with existing RibbonButtonLogic patterns

**Note:** MenuRibbon UC integration to individual forms will be handled manually by user

### Phase 5: SQL Procedures Update

#### 5.1 Core Permission Procedures
**Files to Update:**
- `Modules/Procedures/Permissions/Permission-Inheritance-Logic.sql`
- `Modules/Procedures/Permissions/Permission-Queries.sql`
- All user-related procedures in permissions folder

**Key Changes:**
- Update queries to use users table fields instead of global_permissions table
- Modify permission resolution logic to respect user_perm_overrides flag
- Remove global_permissions table references

#### 5.2 New Permission Resolution Function
- Check global permission from users table fields
- Apply override logic based on user_perm_overrides flag
- Return appropriate permission value for MenuRibbon UC consumption

### Phase 6: Architecture Document Correction

#### 6.1 Update Global Permission System Architecture
**File:** `docs/Global-Permission-System-Architecture.md`
- Correct database schema to reflect users table structure
- Remove global_permissions table references
- Update permission resolution logic documentation
- Document MenuRibbon UC integration pattern
- Remove real-time update requirements

## Implementation Requirements

### Database Status
**Current Users Table:**
- user_perm_overrides field: ✅ EXISTS (boolean, default false)
- Global permission fields: ✅ EXISTS (new_perm, edit_perm, delete_perm, print_perm)

**No Database Changes Required** - Structure is complete.

### UI Control Specifications

#### User Entry Form - Permission Tab
- **Control:** `chkUserPermOverrides` (CheckEdit)
- **Label:** "Enable Permission Overrides"
- **Behavior:** 
  - When checked: User permissions override role permissions
  - When unchecked: User inherits role permissions automatically
  - Linked to `user_perm_overrides` field in database

#### Permission Management Form - User Permissions Tab
- **Control:** `chkPermissionOverride` (CheckEdit, Read-only)
- **Label:** "Override Enabled"
- **Grid Behavior:**
  - Editable only when user_perm_overrides = TRUE
  - Read-only when user_perm_overrides = FALSE (shows inherited role permissions)
  - Visual indicators for override vs inherited permissions

### Final Permission Calculation for MenuRibbon UC

**MenuRibbon UC HasEffectivePermission Method:**

Function HasEffectivePermission(userId, formName, permissionType):
    // Step 1: Check Global Permission (Master Control)
    globalPermission = GetGlobalPermissionFromUsersTable(userId, permissionType)
    // Query: SELECT new_perm/edit_perm/delete_perm/print_perm FROM users WHERE user_id = userId
    if (!globalPermission) return FALSE

    // Step 2: Check Override Flag
    userOverrideEnabled = GetUserOverrideFlagFromUsersTable(userId)
    // Query: SELECT user_perm_overrides FROM users WHERE user_id = userId

    if (userOverrideEnabled):
        // Step 3a: Use User Permission
        userPermission = GetUserPermission(userId, formName, permissionType)
        if (userPermission != NULL) return userPermission

    // Step 3b: Use Role Permission
    rolePermission = GetRolePermission(userId, formName, permissionType)
    if (rolePermission != NULL) return rolePermission

    // Step 4: Default Deny
    return FALSE

**MenuRibbon UC Button Control Logic:**
- New Button: Enabled when HasEffectivePermission("new") && !isEditMode && systemReady
- Edit Button: Enabled when HasEffectivePermission("edit") && !isEditMode && hasSelection
- Delete Button: Enabled when HasEffectivePermission("delete") && !isEditMode && hasSelection
- Print Button: Enabled when HasEffectivePermission("print") && hasData && printSystemReady

## Files to Modify

1. `Forms/ReusableForms/MenuRibbon.cs` - Implement HasEffectivePermission method and integrate with RibbonButtonLogic
2. `Modules/Services/PermissionService.cs` - Update permission resolution logic to support MenuRibbon UC
3. `Modules/Connections/PermissionDatabaseService.cs` - Remove global_permissions table references
4. `Forms/MainForms/PermissionManagementForm.cs` - Add override controls and grid editability logic
5. `Modules/Models/PermissionManagementForm/PermissionModels.cs` - Update models for users table
6. User Entry Form (TBD) - Add override checkbox functionality
7. MenuRibbon UC Algorithm Testing - Test permission calculation logic
8. SQL procedures in `Modules/Procedures/Permissions/` - Update for users table structure
9. `docs/Global-Permission-System-Architecture.md` - Correct database schema documentation

## Success Criteria

1. **MenuRibbon UC:** Final permission calculation works correctly and controls all ribbon buttons
2. **UI:** Override controls function correctly in both forms
3. **Logic:** Permission hierarchy works with proper precedence using users table
4. **Grid Behavior:** User permissions editable only when override enabled
5. **Algorithm Testing:** MenuRibbon UC permission calculation algorithm works correctly
6. **Button Control:** New/Edit/Delete/Print buttons respond correctly to final calculated permissions
7. **Documentation:** Architecture document reflects actual database structure and MenuRibbon UC integration

## Key Corrections Made

1. **MenuRibbon UC Integration:** Central control mechanism for all ribbon buttons across forms
2. **Final Permission Calculation:** HasEffectivePermission method implements complete hierarchy
3. **No Read Permission in Global:** Global permissions control buttons only (New/Edit/Delete/Print)
4. **Grid Editability:** User permissions editable only when user_perm_overrides = TRUE
5. **Database Complete:** No schema changes needed - structure already exists
6. **RibbonButtonLogic Compliance:** Integration follows existing ribbon button logic patterns
7. **No Real-Time Updates:** Changes take effect on application restart

## Next Steps

1. Implement HasEffectivePermission method in MenuRibbon UC
2. Integrate MenuRibbon UC with existing RibbonButtonLogic patterns
3. Identify User Entry Form for override checkbox
4. Test MenuRibbon UC algorithm with various permission scenarios
5. Update business logic to support MenuRibbon UC permission queries
6. Implement grid editability based on override flag
7. Update SQL procedures for users table structure
8. Correct architecture documentation with MenuRibbon UC integration details
