using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Tests for Task 5: Caching and Performance Optimization
    /// Validates enhanced caching, health checks, configuration, and performance monitoring
    /// </summary>
    public static class Task5CachingPerformanceTests
    {
        /// <summary>
        /// Run all Task 5 tests
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== Task 5: Caching and Performance Optimization Tests ===");
            
            try
            {
                TestEnhancedCacheVersioning();
                TestCacheFallbackPaths();
                TestAtomicCacheWrites();
                TestHealthCheckService();
                TestConfigurationService();
                TestPerformanceMonitoring();
                TestCacheCorruptionRecovery();
                
                Console.WriteLine("✅ All Task 5 tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Task 5 tests failed: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Test enhanced cache versioning and migration
        /// </summary>
        private static void TestEnhancedCacheVersioning()
        {
            Console.WriteLine("Testing enhanced cache versioning...");
            
            // Test cache version validation
            var validCache = new FormScanCache
            {
                Version = 1,
                HashingAlgorithm = "SHA256",
                CachedFormList = new List<string> { "TestForm" }
            };
            
            var isValid = FormScanCacheService.ValidateCacheVersion(validCache);
            if (!isValid)
                throw new Exception("Valid cache failed validation");
            
            // Test cache migration
            var legacyCache = new FormScanCache
            {
                Version = 0,
                LastScanTime = DateTime.Now.AddMinutes(-10),
                CachedFormList = new List<string> { "LegacyForm" }
            };
            
            var migratedCache = FormScanCacheService.MigrateCacheVersion(legacyCache);
            if (migratedCache.Version != 1 || migratedCache.HashingAlgorithm != "SHA256")
                throw new Exception("Cache migration failed");
            
            Console.WriteLine("✅ Cache versioning and migration working correctly");
        }
        
        /// <summary>
        /// Test cache fallback paths and write access testing
        /// </summary>
        private static void TestCacheFallbackPaths()
        {
            Console.WriteLine("Testing cache fallback paths...");
            
            try
            {
                // Test cache path retrieval
                var cachePath = FormScanCacheService.GetCacheFilePath();
                if (string.IsNullOrEmpty(cachePath))
                    throw new Exception("Cache path retrieval failed");
                
                // Test directory creation
                var directory = Path.GetDirectoryName(cachePath);
                if (!Directory.Exists(directory))
                    throw new Exception("Cache directory was not created");
                
                Console.WriteLine($"✅ Cache path working: {cachePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Cache path test failed (expected in some environments): {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test atomic cache write operations
        /// </summary>
        private static void TestAtomicCacheWrites()
        {
            Console.WriteLine("Testing atomic cache writes...");
            
            var testForms = new List<string> { "AtomicTestForm1", "AtomicTestForm2" };
            
            try
            {
                // Clear any existing cache
                FormScanCacheService.ClearCache();
                
                // Update cache with test data
                FormScanCacheService.UpdateCache(testForms);
                
                // Verify cache was saved
                var retrievedCache = FormScanCacheService.GetCache();
                if (retrievedCache.CachedFormList.Count != testForms.Count)
                    throw new Exception("Atomic cache write verification failed");
                
                Console.WriteLine("✅ Atomic cache writes working correctly");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Atomic cache write test failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Test health check service functionality
        /// </summary>
        private static async void TestHealthCheckService()
        {
            Console.WriteLine("Testing health check service...");
            
            // Test unauthorized access
            var unauthorizedResult = await HealthCheckService.GetSyncHealthStatus("invalid-key", "localhost");
            if (unauthorizedResult.Status != "Unauthorized")
                throw new Exception("Health check authorization failed");
            
            // Test non-localhost access
            var forbiddenResult = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "remote-host");
            if (forbiddenResult.Status != "Forbidden")
                throw new Exception("Health check localhost restriction failed");
            
            // Test valid access
            var validResult = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost");
            if (string.IsNullOrEmpty(validResult.Status))
                throw new Exception("Health check valid access failed");
            
            Console.WriteLine($"✅ Health check service working correctly - Status: {validResult.Status}");
        }
        
        /// <summary>
        /// Test configuration service functionality
        /// </summary>
        private static void TestConfigurationService()
        {
            Console.WriteLine("Testing configuration service...");
            
            // Test default configuration loading
            var syncConfig = ConfigurationService.GetSyncConfiguration();
            if (syncConfig.TransactionTimeout != TimeSpan.FromMinutes(5))
                throw new Exception("Default sync configuration failed");
            
            var dbConfig = ConfigurationService.GetDatabaseConfiguration();
            if (!dbConfig.UseAdvisoryLocks)
                throw new Exception("Default database configuration failed");
            
            // Test configuration reload
            ConfigurationService.ReloadConfiguration();
            var reloadedConfig = ConfigurationService.GetSyncConfiguration();
            if (reloadedConfig == null)
                throw new Exception("Configuration reload failed");
            
            Console.WriteLine("✅ Configuration service working correctly");
        }
        
        /// <summary>
        /// Test performance monitoring functionality
        /// </summary>
        private static void TestPerformanceMonitoring()
        {
            Console.WriteLine("Testing performance monitoring...");
            
            // Test sync performance measurement
            var metrics = PerformanceMonitoringService.MeasureSyncPerformance(() =>
            {
                // Simulate sync operation
                System.Threading.Thread.Sleep(100);
                return new FormSyncResult
                {
                    MissingForms = new List<string> { "TestForm1" },
                    ObsoleteForms = new List<string>()
                };
            });
            
            if (metrics.TotalSyncTime.TotalMilliseconds < 90)
                throw new Exception("Performance measurement failed");
            
            // Test cache operation measurement
            var cacheTime = PerformanceMonitoringService.MeasureCacheOperation(() =>
            {
                System.Threading.Thread.Sleep(50);
            }, "TestCacheOperation");
            
            if (cacheTime.TotalMilliseconds < 40)
                throw new Exception("Cache operation measurement failed");
            
            // Test memory usage
            var memoryInfo = PerformanceMonitoringService.GetMemoryUsage();
            if (memoryInfo.TotalMemory <= 0)
                throw new Exception("Memory usage measurement failed");
            
            Console.WriteLine("✅ Performance monitoring working correctly");
        }
        
        /// <summary>
        /// Test cache corruption recovery
        /// </summary>
        private static void TestCacheCorruptionRecovery()
        {
            Console.WriteLine("Testing cache corruption recovery...");
            
            try
            {
                // Clear cache first
                FormScanCacheService.ClearCache();
                
                // Test with empty cache
                var emptyCache = FormScanCacheService.GetCache();
                if (emptyCache == null)
                    throw new Exception("Empty cache handling failed");
                
                // Test should skip scan logic
                var shouldSkip = FormScanCacheService.ShouldSkipScan();
                if (shouldSkip) // Should not skip with empty cache
                    throw new Exception("Should skip scan logic failed");
                
                Console.WriteLine("✅ Cache corruption recovery working correctly");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Cache corruption recovery test failed: {ex.Message}");
            }
        }
    }
}
