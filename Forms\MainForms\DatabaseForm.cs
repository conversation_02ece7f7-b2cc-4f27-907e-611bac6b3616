using System;
using System.Configuration;
using System.Drawing;
using System.Diagnostics;
using System.Windows.Forms;
using Npgsql;
using ProManage.Modules.Connections;
using ProManage.Modules.Helpers;

namespace ProManage.Forms
{
    public partial class DatabaseForm : Form
    {
        // Enum for connection status types
        private enum ConnectionStatusType
        {
            NotConfigured,
            Configured,
            Connected,
            Connecting,
            ConnectionFailed,
            Testing,
            TestSuccessful,
            TestFailed,
            Saving,
            SaveSuccessful,
            SaveFailed,
            Cleared
        }

        // Color constants for UI styling
        private readonly Color _primaryButtonColor = Color.FromArgb(0, 122, 204);
        private readonly Color _hoverButtonColor = Color.FromArgb(0, 142, 224);
        private readonly Color _defaultButtonColor = Color.FromArgb(224, 224, 224);
        private readonly Color _successColor = Color.FromArgb(0, 170, 70);
        private readonly Color _errorColor = Color.FromArgb(232, 17, 35);
        private readonly Color _warningColor = Color.FromArgb(255, 128, 0);
        private readonly Color _infoColor = Color.FromArgb(0, 122, 204);

        // Current connection status
        private ConnectionStatusType _currentStatus = ConnectionStatusType.NotConfigured;

        public DatabaseForm()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Handles the form load event
        /// </summary>
        private void DatabaseForm_Load(object sender, EventArgs e)
        {
            // Set up control styles
            SetupControlStyles();

            // Set default port for PostgreSQL if empty
            if (string.IsNullOrWhiteSpace(txtPort.Text))
            {
                txtPort.Text = "5432";  // Default PostgreSQL port
            }

            // Try to determine the initial connection status
            try
            {
                // Check if connection string exists in app.config
                string connString = ConfigurationManager.ConnectionStrings["MyConnection"]?.ConnectionString;

                if (string.IsNullOrWhiteSpace(connString))
                {
                    // No connection string configured
                    SetConnectionStatus(ConnectionStatusType.NotConfigured);
                }
                else if (DatabaseConnectionManager.Instance.IsConnected)
                {
                    // Connection is established
                    SetConnectionStatus(ConnectionStatusType.Connected);
                }
                else
                {
                    // Connection string exists but not connected
                    SetConnectionStatus(ConnectionStatusType.Configured);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error determining connection status: " + ex.Message);
                SetConnectionStatus(ConnectionStatusType.NotConfigured);
            }

            // Load existing connection settings if available
            LoadConnectionSettings();

            // Set up the status update timer to refresh the label every 5 seconds
            // Increased from 2 to 5 seconds to reduce UI flicker and give more time to read status messages
            statusUpdateTimer.Interval = 5000; // 5 seconds
            statusUpdateTimer.Enabled = true;
            Debug.WriteLine("Status update timer started");

            // Set up TextChanged event handlers for all input fields
            txtServer.TextChanged += TextBox_TextChanged;
            txtPort.TextChanged += TextBox_TextChanged;
            txtDatabase.TextChanged += TextBox_TextChanged;
            txtUsername.TextChanged += TextBox_TextChanged;
            txtPassword.TextChanged += TextBox_TextChanged;

            // Validate fields and update button states
            ValidateFieldsAndUpdateButtons();
        }

        /// <summary>
        /// Handles the timer tick event to update the connection status
        /// </summary>
        private void statusUpdateTimer_Tick(object sender, EventArgs e)
        {
            // Only update the status if we're not in a transitional state
            if (_currentStatus != ConnectionStatusType.Testing &&
                _currentStatus != ConnectionStatusType.Saving &&
                _currentStatus != ConnectionStatusType.Connecting)
            {
                UpdateConnectionStatus();
            }
        }

        /// <summary>
        /// Updates the connection status based on the current state
        /// </summary>
        private void UpdateConnectionStatus()
        {
            try
            {
                Debug.WriteLine("Updating connection status...");

                // Check if connection string exists in app.config
                string connString = ConfigurationManager.ConnectionStrings["MyConnection"]?.ConnectionString;

                if (string.IsNullOrWhiteSpace(connString))
                {
                    // No connection string configured
                    _currentStatus = ConnectionStatusType.NotConfigured;
                    DBStatus.Text = "Not Configured";
                    DBStatus.ForeColor = _infoColor;
                    Debug.WriteLine("Connection status: Not Configured (no connection string found)");
                }
                else
                {
                    // Check if the DatabaseConnectionManager is connected
                    bool isConnected = DatabaseConnectionManager.Instance.IsConnected;
                    Debug.WriteLine($"DatabaseConnectionManager.IsConnected reports: {isConnected}");

                    if (isConnected)
                    {
                        // Also verify with a simple query that connection is actually working
                        bool testResult = DatabaseConnectionManager.Instance.TestCurrentConnection();
                        Debug.WriteLine($"Database connection test result: {testResult}");

                        if (testResult)
                        {
                            // Connection is established and working
                            _currentStatus = ConnectionStatusType.Connected;
                            DBStatus.Text = "Connected";
                            DBStatus.ForeColor = _successColor;
                            Debug.WriteLine("Connection status: Connected");
                        }
                        else
                        {
                            // IsConnected is true but test failed
                            _currentStatus = ConnectionStatusType.ConnectionFailed;
                            DBStatus.Text = "Connection Error";
                            DBStatus.ForeColor = _errorColor;
                            Debug.WriteLine("Connection status: Connection Error (IsConnected=true but test failed)");
                        }
                    }
                    else
                    {
                        // Connection string exists but not connected
                        _currentStatus = ConnectionStatusType.Configured;
                        DBStatus.Text = "Not Connected";
                        DBStatus.ForeColor = _errorColor;
                        Debug.WriteLine("Connection status: Not Connected (configured but not connected)");
                    }
                }

                // Force UI update
                this.Update();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating connection status: {ex.Message}");
                // In case of error, show a generic status
                _currentStatus = ConnectionStatusType.ConnectionFailed;
                DBStatus.Text = "Status Error";
                DBStatus.ForeColor = _warningColor;
            }
        }

        /// <summary>
        /// Sets the connection status and updates the UI accordingly
        /// </summary>
        private void SetConnectionStatus(ConnectionStatusType status)
        {
            _currentStatus = status;

            switch (status)
            {
                case ConnectionStatusType.NotConfigured:
                    DBStatus.Text = "Not Configured";
                    DBStatus.ForeColor = _infoColor;
                    break;
                case ConnectionStatusType.Configured:
                    DBStatus.Text = "Not Connected";
                    DBStatus.ForeColor = _errorColor;
                    break;
                case ConnectionStatusType.Connected:
                    DBStatus.Text = "Connected";
                    DBStatus.ForeColor = _successColor;
                    break;
                case ConnectionStatusType.Connecting:
                    DBStatus.Text = "Connecting...";
                    DBStatus.ForeColor = _infoColor;
                    break;
                case ConnectionStatusType.ConnectionFailed:
                    DBStatus.Text = "Connection Failed";
                    DBStatus.ForeColor = _errorColor;
                    break;
                case ConnectionStatusType.Testing:
                    DBStatus.Text = "Testing Connection...";
                    DBStatus.ForeColor = _infoColor;
                    break;
                case ConnectionStatusType.TestSuccessful:
                    DBStatus.Text = "Test Successful";
                    DBStatus.ForeColor = _successColor;
                    break;
                case ConnectionStatusType.TestFailed:
                    DBStatus.Text = "Test Failed";
                    DBStatus.ForeColor = _errorColor;
                    break;
                case ConnectionStatusType.Saving:
                    DBStatus.Text = "Saving...";
                    DBStatus.ForeColor = _infoColor;
                    break;
                case ConnectionStatusType.SaveSuccessful:
                    DBStatus.Text = "Saved Successfully";
                    DBStatus.ForeColor = _successColor;
                    break;
                case ConnectionStatusType.SaveFailed:
                    DBStatus.Text = "Save Failed";
                    DBStatus.ForeColor = _errorColor;
                    break;
                case ConnectionStatusType.Cleared:
                    DBStatus.Text = "Settings Cleared";
                    DBStatus.ForeColor = _infoColor;
                    break;
                default:
                    DBStatus.Text = "Unknown Status";
                    DBStatus.ForeColor = _warningColor;
                    break;
            }

            // Force UI update
            this.Update();
        }

        /// <summary>
        /// Validates all required fields and updates the Save and Test button states
        /// </summary>
        private void ValidateFieldsAndUpdateButtons()
        {
            // Check if all required fields are filled
            bool allFieldsFilled = !string.IsNullOrWhiteSpace(txtServer.Text) &&
                                   !string.IsNullOrWhiteSpace(txtPort.Text) &&
                                   !string.IsNullOrWhiteSpace(txtDatabase.Text) &&
                                   !string.IsNullOrWhiteSpace(txtUsername.Text) &&
                                   !string.IsNullOrWhiteSpace(txtPassword.Text);

            // Enable or disable Save and Test buttons based on validation
            btnSave.Enabled = allFieldsFilled;
            btnTest.Enabled = allFieldsFilled;

            // Update visual state of buttons
            UpdateButtonVisualState(btnSave, allFieldsFilled);
            UpdateButtonVisualState(btnTest, allFieldsFilled);
        }

        /// <summary>
        /// Updates the visual state of a button based on whether it's enabled
        /// </summary>
        private void UpdateButtonVisualState(Button button, bool enabled)
        {
            if (button == btnSave || button == btnTest)
            {
                button.BackColor = enabled ? _primaryButtonColor : Color.FromArgb(200, 200, 200);
                button.ForeColor = enabled ? Color.White : Color.FromArgb(150, 150, 150);
            }
        }

        /// <summary>
        /// Handles text changed events for all input fields
        /// </summary>
        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            ValidateFieldsAndUpdateButtons();
        }

        /// <summary>
        /// Sets up the visual styles for controls
        /// </summary>
        private void SetupControlStyles()
        {
            // Set up hover effects for buttons
            AddButtonHoverEffects(btnSave, _primaryButtonColor, _hoverButtonColor, Color.White);
            AddButtonHoverEffects(btnTest, _infoColor, Color.FromArgb(0, 102, 184), Color.White);
            AddButtonHoverEffects(btnClear, _warningColor, Color.FromArgb(235, 108, 0), Color.White);
            AddButtonHoverEffects(btnCancel, _defaultButtonColor, Color.FromArgb(200, 200, 200), Color.FromArgb(64, 64, 64));

            // Set up focus effects for text boxes
            AddTextBoxFocusEffects(txtServer);
            AddTextBoxFocusEffects(txtPort);
            AddTextBoxFocusEffects(txtDatabase);
            AddTextBoxFocusEffects(txtUsername);
            AddTextBoxFocusEffects(txtPassword);
        }

        /// <summary>
        /// Adds hover effects to a button
        /// </summary>
        private void AddButtonHoverEffects(Button button, Color normalColor, Color hoverColor, Color textColor)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.BackColor = normalColor;
            button.ForeColor = textColor;

            button.MouseEnter += (sender, e) => button.BackColor = hoverColor;
            button.MouseLeave += (sender, e) => button.BackColor = normalColor;
        }

        /// <summary>
        /// Adds focus effects to a text box
        /// </summary>
        private void AddTextBoxFocusEffects(MaskedTextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.BackColor = Color.White;

            textBox.Enter += (sender, e) =>
            {
                textBox.BackColor = Color.FromArgb(240, 248, 255); // Light blue background on focus
            };

            textBox.Leave += (sender, e) =>
            {
                textBox.BackColor = Color.White; // White background when not focused
            };
        }

        /// <summary>
        /// Handles the Save button click event
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            // Disable buttons during save
            btnTest.Enabled = false;
            btnSave.Enabled = false;

            // Update status to Saving
            SetConnectionStatus(ConnectionStatusType.Saving);

            // Refresh UI to show saving status immediately
            this.Update();

            // Validate input values before saving
            if (string.IsNullOrWhiteSpace(txtServer.Text) ||
                string.IsNullOrWhiteSpace(txtPort.Text) ||
                string.IsNullOrWhiteSpace(txtDatabase.Text) ||
                string.IsNullOrWhiteSpace(txtUsername.Text) ||
                string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show(
                    "All fields are required. Please fill in all connection details.",
                    "Validation Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);

                SetConnectionStatus(ConnectionStatusType.SaveFailed);
                ValidateFieldsAndUpdateButtons();
                return;
            }

            // Build the connection string from the user input for PostgreSQL
            // PostgreSQL uses Host and Port as separate parameters
            string newConnString = string.Format("Host={0};Port={1};Database={2};Username={3};Password={4};",
                                                txtServer.Text.Trim(), txtPort.Text.Trim(),
                                                txtDatabase.Text.Trim(), txtUsername.Text.Trim(),
                                                txtPassword.Text.Trim());

            // Save the connection settings using ConfigurationHelper
            try
            {
                Debug.WriteLine("Saving connection string to app.config: " + newConnString);

                // First test the connection before saving
                string errorMessage = string.Empty;
                bool testSuccess = DatabaseConnectionManager.Instance.TestConnection(
                    txtServer.Text.Trim(),
                    txtPort.Text.Trim(),
                    txtDatabase.Text.Trim(),
                    txtUsername.Text.Trim(),
                    txtPassword.Text.Trim(),
                    out errorMessage);

                if (!testSuccess)
                {
                    Debug.WriteLine("Connection test failed before saving: " + errorMessage);

                    DialogResult result = MessageBox.Show(
                        "The test connection failed. Do you still want to save these settings?\n\n" +
                        "Error: " + errorMessage,
                        "Connection Test Failed",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.No)
                    {
                        SetConnectionStatus(ConnectionStatusType.TestFailed);
                        ValidateFieldsAndUpdateButtons();
                        return;
                    }
                }

                // Use the ConfigurationHelper to save database settings
                bool saveResult = ConfigurationHelper.SaveDatabaseSettings(
                    txtServer.Text.Trim(),
                    txtPort.Text.Trim(),
                    txtDatabase.Text.Trim(),
                    txtUsername.Text.Trim(),
                    txtPassword.Text.Trim());

                if (saveResult)
                {
                    Debug.WriteLine("Saved connection string to app.config using ConfigurationHelper");

                    // Verify the connection string was saved correctly
                    string savedConnString = ConfigurationManager.ConnectionStrings["MyConnection"]?.ConnectionString;
                    Debug.WriteLine("Saved connection string: " + savedConnString);

                    // Update the connection manager to use these settings
                    try
                    {
                        bool connectionUpdateResult = DatabaseConnectionManager.Instance.UpdateConnection(
                            txtServer.Text.Trim(),
                            txtPort.Text.Trim(),
                            txtDatabase.Text.Trim(),
                            txtUsername.Text.Trim(),
                            txtPassword.Text.Trim());

                        if (connectionUpdateResult)
                        {
                            // Connection was successfully updated and opened
                            Debug.WriteLine("Connection manager updated with new settings and connection is open");
                            SetConnectionStatus(ConnectionStatusType.SaveSuccessful);

                            MessageBox.Show(
                                "Database connection settings saved successfully and connection is active.",
                                "Save Successful",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);

                            // Set DialogResult to OK to notify the calling form that configuration was successful
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                        else
                        {
                            // Settings were saved but connection failed to open
                            Debug.WriteLine("Connection manager updated but connection failed: " +
                                            DatabaseConnectionManager.Instance.LastError);

                            SetConnectionStatus(ConnectionStatusType.Configured);

                            MessageBox.Show(
                                "Database connection settings saved successfully but connection could not be established.\n\n" +
                                "Error: " + DatabaseConnectionManager.Instance.LastError,
                                "Settings Saved - Connection Failed",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Warning);

                            // Set DialogResult to OK since configuration was saved
                            this.DialogResult = DialogResult.OK;
                            this.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine("Error updating connection manager: " + ex.Message);
                        SetConnectionStatus(ConnectionStatusType.SaveSuccessful);

                        MessageBox.Show(
                            "Database settings were saved, but there was an error updating the active connection.\n\n" +
                            "Error: " + ex.Message + "\n\n" +
                            "You may need to restart the application for changes to take effect.",
                            "Save Successful - Connection Error",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);

                        // Set DialogResult to OK since configuration was saved
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
                else
                {
                    Debug.WriteLine("Failed to save connection string using ConfigurationHelper");
                    SetConnectionStatus(ConnectionStatusType.SaveFailed);

                    MessageBox.Show(
                        "Error saving connection details. Please try again.",
                        "Save Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error saving connection string: " + ex.Message);
                SetConnectionStatus(ConnectionStatusType.SaveFailed);

                MessageBox.Show(
                    "Error saving connection details: " + ex.Message,
                    "Save Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                // Re-enable buttons after save operation
                ValidateFieldsAndUpdateButtons();

                // Update the connection status display immediately
                UpdateConnectionStatus();
            }
        }

        /// <summary>
        /// Handles the Test button click event
        /// </summary>
        private void btnTest_Click(object sender, EventArgs e)
        {
            // Disable buttons during test
            btnTest.Enabled = false;
            btnSave.Enabled = false;

            // Update status to Testing
            SetConnectionStatus(ConnectionStatusType.Testing);

            // Refresh UI to show testing status immediately
            this.Update();

            // Test the connection
            string errorMessage = string.Empty;
            bool isConnected = false;

            try
            {
                Debug.WriteLine("Starting database connection test...");

                // Validate input values before testing
                if (string.IsNullOrWhiteSpace(txtServer.Text) ||
                    string.IsNullOrWhiteSpace(txtPort.Text) ||
                    string.IsNullOrWhiteSpace(txtDatabase.Text) ||
                    string.IsNullOrWhiteSpace(txtUsername.Text) ||
                    string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show(
                        "All fields are required. Please fill in all connection details.",
                        "Validation Error",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);

                    SetConnectionStatus(ConnectionStatusType.TestFailed);
                    ValidateFieldsAndUpdateButtons();
                    return;
                }

                // Use the DatabaseConnectionManager to test the connection
                isConnected = DatabaseConnectionManager.Instance.TestConnection(
                    txtServer.Text.Trim(),
                    txtPort.Text.Trim(),
                    txtDatabase.Text.Trim(),
                    txtUsername.Text.Trim(),
                    txtPassword.Text.Trim(),
                    out errorMessage);

                Debug.WriteLine($"Test connection result: {isConnected}, Error: {errorMessage}");
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                isConnected = false;
                Debug.WriteLine($"Exception during connection test: {ex.Message}");
            }

            // Update the status based on the test result
            if (isConnected)
            {
                SetConnectionStatus(ConnectionStatusType.TestSuccessful);

                MessageBox.Show(
                    "Connection test successful. You can now save these settings.",
                    "Connection Test",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            else
            {
                SetConnectionStatus(ConnectionStatusType.TestFailed);

                // Provide a more detailed error message with troubleshooting tips
                string detailedMessage = "Connection test failed.\n\n";

                if (string.IsNullOrEmpty(errorMessage))
                {
                    errorMessage = "Unknown error occurred. Check server logs for details.";
                }

                if (errorMessage.Contains("timeout"))
                {
                    detailedMessage += "Troubleshooting tips:\n" +
                                      "• Check if the server is running\n" +
                                      "• Verify the server address is correct\n" +
                                      "• Check if a firewall is blocking the connection\n\n";
                }
                else if (errorMessage.Contains("authentication"))
                {
                    detailedMessage += "Troubleshooting tips:\n" +
                                      "• Verify your username and password\n" +
                                      "• Check if the user has access to the database\n\n";
                }
                else if (errorMessage.Contains("database") && errorMessage.Contains("does not exist"))
                {
                    detailedMessage += "Troubleshooting tips:\n" +
                                      "• Verify the database name is correct\n" +
                                      "• Check if the database needs to be created first\n\n";
                }

                detailedMessage += "Error details: " + errorMessage;

                MessageBox.Show(
                    detailedMessage,
                    "Connection Test Failed",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }

            // Re-enable buttons after test
            ValidateFieldsAndUpdateButtons();
        }

        /// <summary>
        /// Handles the Cancel button click event
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            // Close the form without saving
            this.Close();
        }

        /// <summary>
        /// Handles the Clear button click event
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            // Ask for confirmation before clearing settings
            string confirmMessage = "Are you sure you want to clear all database settings?\n\n" +
                                   "This will remove the connection string from the application configuration.";

            // Ask for confirmation before clearing settings
            DialogResult result = MessageBox.Show(
                confirmMessage,
                "Clear Database Settings",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.No)
            {
                return;
            }

            // Clear all input fields
            ClearFields();

            // Remove the connection string from app.config
            try
            {
                // Use the ConfigurationHelper to clear database settings
                if (ConfigurationHelper.ClearDatabaseSettings())
                {
                    Debug.WriteLine("Removed connection string from app.config using ConfigurationHelper");
                }

                // Update the status
                SetConnectionStatus(ConnectionStatusType.Cleared);
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error clearing connection settings: " + ex.Message);
                MessageBox.Show(
                    "Error clearing connection settings: " + ex.Message,
                    "Clear Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Clears all input fields
        /// </summary>
        private void ClearFields()
        {
            txtServer.Text = string.Empty;
            txtPort.Text = "5432"; // Default PostgreSQL port
            txtDatabase.Text = string.Empty;
            txtUsername.Text = string.Empty;
            txtPassword.Text = string.Empty;

            // Validate fields and update button states
            ValidateFieldsAndUpdateButtons();
        }

        /// <summary>
        /// Loads existing connection settings from app.config or Development.config in debug mode
        /// </summary>
        private void LoadConnectionSettings()
        {
            try
            {
                // Use the ConfigurationHelper to load database settings
                var settings = ConfigurationHelper.LoadDatabaseSettings();

                if (settings != null)
                {
                    // Populate the form fields with the loaded settings
                    if (settings.ContainsKey("Host"))
                        txtServer.Text = settings["Host"];
                    if (settings.ContainsKey("Port"))
                        txtPort.Text = settings["Port"];
                    if (settings.ContainsKey("Database"))
                        txtDatabase.Text = settings["Database"];
                    if (settings.ContainsKey("Username"))
                        txtUsername.Text = settings["Username"];
                    if (settings.ContainsKey("Password"))
                        txtPassword.Text = settings["Password"];

                    Debug.WriteLine("Connection settings loaded successfully");
                    Debug.WriteLine($"Server: {txtServer.Text}, Port: {txtPort.Text}, Database: {txtDatabase.Text}, Username: {txtUsername.Text}");

                    // In debug mode, automatically save these settings to app.config
                    // This ensures the settings persist even if the user doesn't manually save them
#if DEBUG
                    SaveDevelopmentSettingsToAppConfig();
#endif
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error loading connection settings: " + ex.Message);
                // Don't show an error message to the user - just leave the fields empty
            }
        }

        /// <summary>
        /// Automatically saves the development settings to app.config
        /// This ensures the settings persist even if the user doesn't manually save them
        /// Only used in DEBUG mode
        /// </summary>
        private void SaveDevelopmentSettingsToAppConfig()
        {
#if DEBUG
            try
            {
                Debug.WriteLine("Automatically saving development settings to app.config");

                // Use the ConfigurationHelper to save database settings
                bool saveResult = ConfigurationHelper.SaveDatabaseSettings(
                    txtServer.Text,
                    txtPort.Text,
                    txtDatabase.Text,
                    txtUsername.Text,
                    txtPassword.Text);

                if (saveResult)
                {
                    Debug.WriteLine("Automatically saved development settings to app.config");
                }
                else
                {
                    Debug.WriteLine("Failed to automatically save development settings");
                }

                // Also update the connection manager to use these settings
                try
                {
                    DatabaseConnectionManager.Instance.UpdateConnection(
                        txtServer.Text,
                        txtPort.Text,
                        txtDatabase.Text,
                        txtUsername.Text,
                        txtPassword.Text);

                    // Start connection monitoring
                    DatabaseConnectionManager.Instance.StartConnectionMonitoring();

                    Debug.WriteLine("Connection manager updated with development settings");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine("Error updating connection manager: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error automatically saving development settings: " + ex.Message);
                // Don't show an error message to the user - this is a background operation
            }
#endif
        }
    }
}
