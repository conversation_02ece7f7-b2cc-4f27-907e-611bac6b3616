// UserMasterForm Navigation Service - Clean navigation functionality for users
// Usage: Provides navigation operations (First, Previous, Next, Last) with proper state management

using System;
using System.Diagnostics;
using System.Windows.Forms;
using Npgsql;
using ProManage.Modules.Models.UserMasterForm;
using ProManage.Modules.Connections;
using ProManage.Modules.Data;
using ProManage.Modules.Data.UserMasterForm;
using ProManage.Modules.Helpers;
using ProManage.Modules.UI;

namespace ProManage.Modules.Helpers.UserMasterForm
{
    /// <summary>
    /// Navigation helper for UserMasterForm
    /// Provides First, Previous, Next, Last navigation functionality
    /// </summary>
    public static class UserMasterFormNavigation
    {
        /// <summary>
        /// Navigates to the first user in the database
        /// </summary>
        /// <returns>First user or null if none found</returns>
        public static UserMasterFormModel NavigateToFirst()
        {
            Debug.WriteLine("UserMasterFormNavigation: Navigating to first user");

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_FIRST_USER);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var user = MapUserFromReader(reader);
                                Debug.WriteLine($"Found first user: ID={user.UserId}, Username={user.Username}");
                                return user;
                            }
                        }
                    }
                }

                Debug.WriteLine("No users found in database");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to first user: {ex.Message}");
                throw new Exception($"Error navigating to first user: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the last user in the database
        /// </summary>
        /// <returns>Last user or null if none found</returns>
        public static UserMasterFormModel NavigateToLast()
        {
            Debug.WriteLine("UserMasterFormNavigation: Navigating to last user");

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_LAST_USER);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var user = MapUserFromReader(reader);
                                Debug.WriteLine($"Found last user: ID={user.UserId}, Username={user.Username}");
                                return user;
                            }
                        }
                    }
                }

                Debug.WriteLine("No users found in database");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to last user: {ex.Message}");
                throw new Exception($"Error navigating to last user: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the previous user before the given ID
        /// </summary>
        /// <param name="currentUserId">Current user ID</param>
        /// <returns>Previous user or null if none found</returns>
        public static UserMasterFormModel NavigateToPrevious(int currentUserId)
        {
            Debug.WriteLine($"UserMasterFormNavigation: Navigating to previous user before ID {currentUserId}");

            if (currentUserId <= 0)
            {
                throw new ArgumentException("Invalid current user ID");
            }

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_PREVIOUS_USER);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@current_id", currentUserId);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var user = MapUserFromReader(reader);
                                Debug.WriteLine($"Found previous user: ID={user.UserId}, Username={user.Username}");
                                return user;
                            }
                        }
                    }
                }

                Debug.WriteLine("No previous user found - already at first");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to previous user: {ex.Message}");
                throw new Exception($"Error navigating to previous user: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the next user after the given ID
        /// </summary>
        /// <param name="currentUserId">Current user ID</param>
        /// <returns>Next user or null if none found</returns>
        public static UserMasterFormModel NavigateToNext(int currentUserId)
        {
            Debug.WriteLine($"UserMasterFormNavigation: Navigating to next user after ID {currentUserId}");

            if (currentUserId <= 0)
            {
                throw new ArgumentException("Invalid current user ID");
            }

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_NEXT_USER);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@current_id", currentUserId);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var user = MapUserFromReader(reader);
                                Debug.WriteLine($"Found next user: ID={user.UserId}, Username={user.Username}");
                                return user;
                            }
                        }
                    }
                }

                Debug.WriteLine("No next user found - already at last");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to next user: {ex.Message}");
                throw new Exception($"Error navigating to next user: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Gets the total count of users in the database
        /// </summary>
        /// <returns>Total user count</returns>
        public static int GetUserCount()
        {
            try
            {
                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_USER_COUNT);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        var result = cmd.ExecuteScalar();
                        int count = Convert.ToInt32(result);
                        Debug.WriteLine($"Total user count: {count}");
                        return count;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Gets the position of a user in the ordered list
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Position (1-based) or 0 if not found</returns>
        public static int GetUserPosition(int userId)
        {
            try
            {
                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.User.MODULE_NAME,
                    "UserNavigation",
                    SQLQueries.User.UserNavigation.GET_USER_POSITION);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@user_id", userId);
                        var result = cmd.ExecuteScalar();
                        int position = Convert.ToInt32(result);
                        Debug.WriteLine($"User ID {userId} position: {position}");
                        return position;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user position: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Updates the form title with navigation position
        /// </summary>
        /// <param name="form">The form to update</param>
        /// <param name="currentUser">Current user</param>
        public static void UpdateNavigationPosition(dynamic form, UserMasterFormModel currentUser)
        {
            try
            {
                int totalCount = GetUserCount();
                int currentPosition = 0;

                if (currentUser != null && currentUser.UserId > 0)
                {
                    currentPosition = GetUserPosition(currentUser.UserId);
                }

                if (totalCount > 0 && currentPosition > 0)
                {
                    form.Text = $"User Entry - Record {currentPosition} of {totalCount}";
                }
                else
                {
                    form.Text = "User Entry";
                }

                Debug.WriteLine($"Navigation position updated: {currentPosition} of {totalCount}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating navigation position: {ex.Message}");
                form.Text = "User Entry";
            }
        }

        /// <summary>
        /// Maps user data from database reader
        /// </summary>
        /// <param name="reader">Database reader</param>
        /// <returns>User model</returns>
        private static UserMasterFormModel MapUserFromReader(NpgsqlDataReader reader)
        {
            return new UserMasterFormModel
            {
                UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                Username = reader.IsDBNull(reader.GetOrdinal("username")) ? string.Empty : reader.GetString(reader.GetOrdinal("username")),
                FullName = reader.IsDBNull(reader.GetOrdinal("full_name")) ? string.Empty : reader.GetString(reader.GetOrdinal("full_name")),
                Email = reader.IsDBNull(reader.GetOrdinal("email")) ? string.Empty : reader.GetString(reader.GetOrdinal("email")),
                Role = reader.IsDBNull(reader.GetOrdinal("role")) ? "User" : reader.GetString(reader.GetOrdinal("role")),
                Department = reader.IsDBNull(reader.GetOrdinal("department")) ? string.Empty : reader.GetString(reader.GetOrdinal("department")),
                Phone = reader.IsDBNull(reader.GetOrdinal("phone")) ? string.Empty : reader.GetString(reader.GetOrdinal("phone")),
                Designation = reader.IsDBNull(reader.GetOrdinal("designation")) ? string.Empty : reader.GetString(reader.GetOrdinal("designation")),
                ShortName = reader.IsDBNull(reader.GetOrdinal("short_name")) ? string.Empty : reader.GetString(reader.GetOrdinal("short_name")),
                PhotoPath = reader.IsDBNull(reader.GetOrdinal("photo_path")) ? string.Empty : reader.GetString(reader.GetOrdinal("photo_path")),
                PasswordHash = reader.IsDBNull(reader.GetOrdinal("password_hash")) ? string.Empty : reader.GetString(reader.GetOrdinal("password_hash")),
                PasswordSalt = reader.IsDBNull(reader.GetOrdinal("password_salt")) ? string.Empty : reader.GetString(reader.GetOrdinal("password_salt")),
                IsActive = reader.IsDBNull(reader.GetOrdinal("is_active")) ? true : reader.GetBoolean(reader.GetOrdinal("is_active")),
                LastLoginDate = reader.IsDBNull(reader.GetOrdinal("last_login_date")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("last_login_date")),
                CreatedDate = reader.IsDBNull(reader.GetOrdinal("created_date")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("created_date")),
                CanRead = reader.IsDBNull(reader.GetOrdinal("can_read")) ? true : reader.GetBoolean(reader.GetOrdinal("can_read")),
                CanCreate = reader.IsDBNull(reader.GetOrdinal("can_create")) ? true : reader.GetBoolean(reader.GetOrdinal("can_create")),
                CanEdit = reader.IsDBNull(reader.GetOrdinal("can_edit")) ? true : reader.GetBoolean(reader.GetOrdinal("can_edit")),
                CanDelete = reader.IsDBNull(reader.GetOrdinal("can_delete")) ? true : reader.GetBoolean(reader.GetOrdinal("can_delete")),
                CanPrint = reader.IsDBNull(reader.GetOrdinal("can_print")) ? true : reader.GetBoolean(reader.GetOrdinal("can_print"))
            };
        }
    }
}
