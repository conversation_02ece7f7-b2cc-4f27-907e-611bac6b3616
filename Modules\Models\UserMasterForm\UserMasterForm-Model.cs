using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.UserMasterForm
{
    /// <summary>
    /// Data model for user management in UserMasterForm
    /// </summary>
    public class UserMasterFormModel
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Username (unique, required)
        /// </summary>
        [Required(ErrorMessage = "Username is required")]
        [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
        public string Username { get; set; }

        /// <summary>
        /// User's full name (required)
        /// </summary>
        [Required(ErrorMessage = "Full name is required")]
        [StringLength(100, ErrorMessage = "Full name cannot exceed 100 characters")]
        public string FullName { get; set; }

        /// <summary>
        /// User's email address (required, unique)
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string Email { get; set; }

        /// <summary>
        /// User's role/position
        /// </summary>
        [StringLength(20, ErrorMessage = "Role cannot exceed 20 characters")]
        public string Role { get; set; }

        /// <summary>
        /// User's department
        /// </summary>
        [StringLength(50, ErrorMessage = "Department cannot exceed 50 characters")]
        public string Department { get; set; }

        /// <summary>
        /// User's phone number
        /// </summary>
        [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
        public string Phone { get; set; }

        /// <summary>
        /// User's designation/job title
        /// </summary>
        [StringLength(50, ErrorMessage = "Designation cannot exceed 50 characters")]
        public string Designation { get; set; }

        /// <summary>
        /// User's short display name
        /// </summary>
        [StringLength(50, ErrorMessage = "Short name cannot exceed 50 characters")]
        public string ShortName { get; set; }

        /// <summary>
        /// User's photo (base64 encoded or file path)
        /// </summary>
        public string PhotoPath { get; set; }

        /// <summary>
        /// Password hash (for new users or password changes)
        /// </summary>
        public string PasswordHash { get; set; }

        /// <summary>
        /// Password salt (for new users or password changes)
        /// </summary>
        public string PasswordSalt { get; set; }

        /// <summary>
        /// Whether the user is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Last login date
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// Global permission to read/view forms
        /// </summary>
        public bool CanRead { get; set; } = true;

        /// <summary>
        /// Global permission to create new records
        /// </summary>
        public bool CanCreate { get; set; } = true;

        /// <summary>
        /// Global permission to edit existing records
        /// </summary>
        public bool CanEdit { get; set; } = true;

        /// <summary>
        /// Global permission to delete records
        /// </summary>
        public bool CanDelete { get; set; } = true;

        /// <summary>
        /// Global permission to print reports
        /// </summary>
        public bool CanPrint { get; set; } = true;



        /// <summary>
        /// Constructor
        /// </summary>
        public UserMasterFormModel()
        {
            IsActive = true; // Default to active
            Role = "User"; // Default role
        }

        /// <summary>
        /// Validates the model
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(FullName) &&
                   !string.IsNullOrWhiteSpace(Email) &&
                   IsValidEmail(Email);
        }

        /// <summary>
        /// Validates email format
        /// </summary>
        /// <param name="email">Email to validate</param>
        /// <returns>True if valid email format</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets display text for the user
        /// </summary>
        public string DisplayText => $"{FullName} ({Username})";

        /// <summary>
        /// Gets status text
        /// </summary>
        public string StatusText => IsActive ? "Active" : "Inactive";

        /// <summary>
        /// Creates a copy of the model for editing
        /// </summary>
        /// <returns>Copy of the model</returns>
        public UserMasterFormModel Clone()
        {
            return new UserMasterFormModel
            {
                UserId = this.UserId,
                Username = this.Username,
                FullName = this.FullName,
                ShortName = this.ShortName,
                Email = this.Email,
                Phone = this.Phone,
                Role = this.Role,
                Department = this.Department,
                Designation = this.Designation,
                PhotoPath = this.PhotoPath,
                PasswordHash = this.PasswordHash,
                PasswordSalt = this.PasswordSalt,
                IsActive = this.IsActive,
                LastLoginDate = this.LastLoginDate,
                CreatedDate = this.CreatedDate,
                CanRead = this.CanRead,
                CanCreate = this.CanCreate,
                CanEdit = this.CanEdit,
                CanDelete = this.CanDelete,
                CanPrint = this.CanPrint
            };
        }
    }
}
