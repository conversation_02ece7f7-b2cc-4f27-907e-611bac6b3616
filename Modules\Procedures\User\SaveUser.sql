-- SaveUser.sql
-- Contains queries for saving user information

-- [InsertUser] --
INSERT INTO users (
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password,
    is_active,
    role_id,
    new_perm,
    edit_perm,
    delete_perm,
    print_perm
) VALUES (
    @username,
    @password_hash,
    @password_salt,
    @full_name,
    @email,
    @role,
    @department,
    @phone,
    @designation,
    @short_name,
    @photo_path,
    @edit_password,
    @is_active,
    @role_id,
    @can_create,
    @can_edit,
    @can_delete,
    @can_print
)
RETURNING user_id;
-- [End] --

-- [UpdateUser] --
UPDATE users
SET
    username = @username,
    full_name = @full_name,
    email = @email,
    role = @role,
    department = @department,
    phone = @phone,
    designation = @designation,
    short_name = @short_name,
    photo_path = @photo_path,
    is_active = @is_active,
    new_perm = @can_create,
    edit_perm = @can_edit,
    delete_perm = @can_delete,
    print_perm = @can_print
WHERE
    user_id = @user_id;
-- [End] --

-- [UpdateUserPassword] --
UPDATE users
SET
    password_hash = @password_hash,
    password_salt = @password_salt
WHERE
    user_id = @user_id;
-- [End] --

-- [UpdateEditPassword] --
UPDATE users
SET
    edit_password = @edit_password
WHERE
    user_id = @user_id;
-- [End] --

-- [UpdateLastLogin] --
UPDATE users
SET
    last_login_date = CURRENT_TIMESTAMP
WHERE
    user_id = @user_id;
-- [End] --

-- [DeleteUser] --
UPDATE users
SET
    is_active = FALSE
WHERE
    user_id = @user_id;
-- [End] --
