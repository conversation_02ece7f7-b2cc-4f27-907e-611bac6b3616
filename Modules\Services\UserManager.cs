using System;
using System.Diagnostics;
using ProManage.Modules.Models.LoginForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Singleton service for managing the current user session
    /// Provides centralized user management across the application
    /// </summary>
    public sealed class UserManager
    {
        #region Singleton Implementation

        private static readonly Lazy<UserManager> _instance = 
            new Lazy<UserManager>(() => new UserManager());

        /// <summary>
        /// Gets the singleton instance of the UserManager
        /// </summary>
        public static UserManager Instance => _instance.Value;

        #endregion

        #region Private Fields

        private LoginFormUserModel _currentUser;
        private readonly object _lockObject = new object();

        #endregion

        #region Constructor

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private UserManager()
        {
            Debug.WriteLine("UserManager: Instance created");
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the current logged-in user
        /// </summary>
        public LoginFormUserModel CurrentUser
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentUser;
                }
            }
        }

        /// <summary>
        /// Gets whether a user is currently logged in
        /// </summary>
        public bool IsUserLoggedIn
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentUser != null;
                }
            }
        }

        /// <summary>
        /// Gets the current user's ID
        /// </summary>
        public int CurrentUserId
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentUser?.Id ?? 0;
                }
            }
        }

        /// <summary>
        /// Gets the current user's username
        /// </summary>
        public string CurrentUsername
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentUser?.Username ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// Gets the current user's full name
        /// </summary>
        public string CurrentUserFullName
        {
            get
            {
                lock (_lockObject)
                {
                    return _currentUser?.FullName ?? string.Empty;
                }
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Sets the current user for the session
        /// </summary>
        /// <param name="user">The user to set as current</param>
        public void SetCurrentUser(LoginFormUserModel user)
        {
            lock (_lockObject)
            {
                _currentUser = user;
                Debug.WriteLine($"UserManager: Current user set to {user?.Username ?? "null"}");
            }
        }

        /// <summary>
        /// Clears the current user (logout)
        /// </summary>
        public void ClearCurrentUser()
        {
            lock (_lockObject)
            {
                var previousUser = _currentUser?.Username ?? "null";
                _currentUser = null;
                Debug.WriteLine($"UserManager: Current user cleared (was {previousUser})");
            }
        }

        /// <summary>
        /// Updates the current user's information
        /// </summary>
        /// <param name="user">Updated user information</param>
        public void UpdateCurrentUser(LoginFormUserModel user)
        {
            lock (_lockObject)
            {
                if (_currentUser != null && user != null && _currentUser.Id == user.Id)
                {
                    _currentUser = user;
                    Debug.WriteLine($"UserManager: Current user updated for {user.Username}");
                }
                else
                {
                    Debug.WriteLine("UserManager: Cannot update current user - user mismatch or null");
                }
            }
        }

        /// <summary>
        /// Checks if the current user has the specified ID
        /// </summary>
        /// <param name="userId">User ID to check</param>
        /// <returns>True if current user has the specified ID</returns>
        public bool IsCurrentUser(int userId)
        {
            lock (_lockObject)
            {
                return _currentUser?.Id == userId;
            }
        }

        /// <summary>
        /// Checks if the current user has the specified username
        /// </summary>
        /// <param name="username">Username to check</param>
        /// <returns>True if current user has the specified username</returns>
        public bool IsCurrentUser(string username)
        {
            lock (_lockObject)
            {
                return string.Equals(_currentUser?.Username, username, StringComparison.OrdinalIgnoreCase);
            }
        }

        #endregion

        #region Session Management

        /// <summary>
        /// Gets session information for debugging
        /// </summary>
        /// <returns>Session information string</returns>
        public string GetSessionInfo()
        {
            lock (_lockObject)
            {
                if (_currentUser == null)
                {
                    return "No user logged in";
                }

                return $"User: {_currentUser.Username} (ID: {_currentUser.Id}), " +
                       $"Full Name: {_currentUser.FullName}, " +
                       $"Active: {_currentUser.IsActive}";
            }
        }

        #endregion
    }
}
