﻿namespace ProManage.Forms
{
    partial class UserMasterForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuRibbon = new ProManage.Forms.ReusableForms.MenuRibbon();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.groupSecurity = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.lblEditPassword = new DevExpress.XtraEditors.LabelControl();
            this.txtEditPassword = new DevExpress.XtraEditors.TextEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lblPassword = new DevExpress.XtraEditors.LabelControl();
            this.txtPassword = new DevExpress.XtraEditors.TextEdit();
            this.lblConfirmPassword = new DevExpress.XtraEditors.LabelControl();
            this.txtConfirmPassword = new DevExpress.XtraEditors.TextEdit();
            this.groupUserInfo = new DevExpress.XtraEditors.GroupControl();
            this.groupPhoto = new DevExpress.XtraEditors.GroupControl();
            this.btnRemovePhoto = new DevExpress.XtraEditors.SimpleButton();
            this.btnBrowsePhoto = new DevExpress.XtraEditors.SimpleButton();
            this.pictureUser = new DevExpress.XtraEditors.PictureEdit();
            this.chkIsActive = new DevExpress.XtraEditors.CheckEdit();
            this.txtDesignation = new DevExpress.XtraEditors.TextEdit();
            this.lblDesignation = new DevExpress.XtraEditors.LabelControl();
            this.cmbDepartment = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblDepartment = new DevExpress.XtraEditors.LabelControl();
            this.cmbRole = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblRole = new DevExpress.XtraEditors.LabelControl();
            this.txtPhone = new DevExpress.XtraEditors.TextEdit();
            this.lblPhone = new DevExpress.XtraEditors.LabelControl();
            this.txtEmail = new DevExpress.XtraEditors.TextEdit();
            this.lblEmail = new DevExpress.XtraEditors.LabelControl();
            this.txtShortName = new DevExpress.XtraEditors.TextEdit();
            this.lblShortName = new DevExpress.XtraEditors.LabelControl();
            this.txtFullName = new DevExpress.XtraEditors.TextEdit();
            this.lblFullName = new DevExpress.XtraEditors.LabelControl();
            this.txtUsername = new DevExpress.XtraEditors.TextEdit();
            this.lblUsername = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.groupGlobalPermissions = new DevExpress.XtraEditors.GroupControl();
            this.groupActionPermissions = new DevExpress.XtraEditors.GroupControl();
            this.chkPrintAction = new DevExpress.XtraEditors.CheckEdit();
            this.chkExportAction = new DevExpress.XtraEditors.CheckEdit();
            this.chkDeleteAction = new DevExpress.XtraEditors.CheckEdit();
            this.chkUpdateAction = new DevExpress.XtraEditors.CheckEdit();
            this.chkNewAction = new DevExpress.XtraEditors.CheckEdit();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.groupSystemPreferences = new DevExpress.XtraEditors.GroupControl();
            this.chkSoundNotifications = new DevExpress.XtraEditors.CheckEdit();
            this.chkEmailNotifications = new DevExpress.XtraEditors.CheckEdit();
            this.cmbTimeFormat = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblTimeFormat = new DevExpress.XtraEditors.LabelControl();
            this.cmbDateFormat = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblDateFormat = new DevExpress.XtraEditors.LabelControl();
            this.groupUIPreferences = new DevExpress.XtraEditors.GroupControl();
            this.chkShowNotifications = new DevExpress.XtraEditors.CheckEdit();
            this.chkAutoSave = new DevExpress.XtraEditors.CheckEdit();
            this.cmbLanguage = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblLanguage = new DevExpress.XtraEditors.LabelControl();
            this.cmbDefaultTheme = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblDefaultTheme = new DevExpress.XtraEditors.LabelControl();
            this.chkUserPermOverrides = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupSecurity)).BeginInit();
            this.groupSecurity.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtEditPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtConfirmPassword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupUserInfo)).BeginInit();
            this.groupUserInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupPhoto)).BeginInit();
            this.groupPhoto.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureUser.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDesignation.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDepartment.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRole.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShortName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFullName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUsername.Properties)).BeginInit();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupGlobalPermissions)).BeginInit();
            this.groupGlobalPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupActionPermissions)).BeginInit();
            this.groupActionPermissions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintAction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExportAction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDeleteAction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUpdateAction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNewAction.Properties)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupSystemPreferences)).BeginInit();
            this.groupSystemPreferences.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSoundNotifications.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkEmailNotifications.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTimeFormat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDateFormat.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupUIPreferences)).BeginInit();
            this.groupUIPreferences.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowNotifications.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkAutoSave.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbLanguage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDefaultTheme.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUserPermOverrides.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // menuRibbon
            // 
            this.menuRibbon.CurrentUserId = 1;
            this.menuRibbon.Dock = System.Windows.Forms.DockStyle.Top;
            this.menuRibbon.FormName = "UserMasterForm";
            this.menuRibbon.HasUnsavedChanges = false;
            this.menuRibbon.IsEditMode = false;
            this.menuRibbon.Location = new System.Drawing.Point(2, 2);
            this.menuRibbon.Name = "menuRibbon";
            this.menuRibbon.Size = new System.Drawing.Size(1094, 131);
            this.menuRibbon.TabIndex = 0;
            // 
            // panelControl1
            // 
            this.panelControl1.Controls.Add(this.panelControl2);
            this.panelControl1.Controls.Add(this.menuRibbon);
            this.panelControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControl1.Location = new System.Drawing.Point(0, 0);
            this.panelControl1.Name = "panelControl1";
            this.panelControl1.Size = new System.Drawing.Size(1098, 561);
            this.panelControl1.TabIndex = 1;
            // 
            // panelControl2
            // 
            this.panelControl2.Controls.Add(this.xtraTabControl1);
            this.panelControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelControl2.Location = new System.Drawing.Point(2, 133);
            this.panelControl2.Name = "panelControl2";
            this.panelControl2.Size = new System.Drawing.Size(1094, 426);
            this.panelControl2.TabIndex = 2;
            // 
            // xtraTabControl1
            // 
            this.xtraTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.xtraTabControl1.Location = new System.Drawing.Point(2, 2);
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.xtraTabPage1;
            this.xtraTabControl1.Size = new System.Drawing.Size(1090, 422);
            this.xtraTabControl1.TabIndex = 2;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.groupSecurity);
            this.xtraTabPage1.Controls.Add(this.groupUserInfo);
            this.xtraTabPage1.Name = "xtraTabPage1";
            this.xtraTabPage1.Size = new System.Drawing.Size(1088, 397);
            this.xtraTabPage1.Text = "General";
            // 
            // groupSecurity
            // 
            this.groupSecurity.Controls.Add(this.groupControl2);
            this.groupSecurity.Controls.Add(this.groupControl1);
            this.groupSecurity.Location = new System.Drawing.Point(12, 230);
            this.groupSecurity.Name = "groupSecurity";
            this.groupSecurity.Size = new System.Drawing.Size(1073, 161);
            this.groupSecurity.TabIndex = 1;
            this.groupSecurity.Text = "Security";
            // 
            // groupControl2
            // 
            this.groupControl2.Controls.Add(this.lblEditPassword);
            this.groupControl2.Controls.Add(this.txtEditPassword);
            this.groupControl2.Controls.Add(this.textEdit1);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.Location = new System.Drawing.Point(347, 26);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(333, 130);
            this.groupControl2.TabIndex = 9;
            this.groupControl2.Text = "Edit Password";
            // 
            // lblEditPassword
            // 
            this.lblEditPassword.Location = new System.Drawing.Point(8, 39);
            this.lblEditPassword.Name = "lblEditPassword";
            this.lblEditPassword.Size = new System.Drawing.Size(76, 15);
            this.lblEditPassword.TabIndex = 4;
            this.lblEditPassword.Text = "Edit Password:";
            // 
            // txtEditPassword
            // 
            this.txtEditPassword.Location = new System.Drawing.Point(121, 36);
            this.txtEditPassword.Name = "txtEditPassword";
            this.txtEditPassword.Properties.UseSystemPasswordChar = true;
            this.txtEditPassword.Size = new System.Drawing.Size(200, 22);
            this.txtEditPassword.TabIndex = 5;
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(121, 70);
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.UseSystemPasswordChar = true;
            this.textEdit1.Size = new System.Drawing.Size(200, 22);
            this.textEdit1.TabIndex = 7;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(8, 73);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(100, 15);
            this.labelControl1.TabIndex = 6;
            this.labelControl1.Text = "Confirm Password:";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.lblPassword);
            this.groupControl1.Controls.Add(this.txtPassword);
            this.groupControl1.Controls.Add(this.lblConfirmPassword);
            this.groupControl1.Controls.Add(this.txtConfirmPassword);
            this.groupControl1.Location = new System.Drawing.Point(5, 26);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(333, 130);
            this.groupControl1.TabIndex = 8;
            this.groupControl1.Text = "Login Password";
            // 
            // lblPassword
            // 
            this.lblPassword.Location = new System.Drawing.Point(5, 35);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new System.Drawing.Size(53, 15);
            this.lblPassword.TabIndex = 0;
            this.lblPassword.Text = "Password:";
            // 
            // txtPassword
            // 
            this.txtPassword.Location = new System.Drawing.Point(128, 32);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Properties.UseSystemPasswordChar = true;
            this.txtPassword.Size = new System.Drawing.Size(200, 22);
            this.txtPassword.TabIndex = 1;
            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.Location = new System.Drawing.Point(5, 69);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new System.Drawing.Size(100, 15);
            this.lblConfirmPassword.TabIndex = 2;
            this.lblConfirmPassword.Text = "Confirm Password:";
            // 
            // txtConfirmPassword
            // 
            this.txtConfirmPassword.Location = new System.Drawing.Point(128, 66);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.Properties.UseSystemPasswordChar = true;
            this.txtConfirmPassword.Size = new System.Drawing.Size(200, 22);
            this.txtConfirmPassword.TabIndex = 3;
            // 
            // groupUserInfo
            // 
            this.groupUserInfo.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupUserInfo.Controls.Add(this.groupPhoto);
            this.groupUserInfo.Controls.Add(this.chkIsActive);
            this.groupUserInfo.Controls.Add(this.txtDesignation);
            this.groupUserInfo.Controls.Add(this.lblDesignation);
            this.groupUserInfo.Controls.Add(this.cmbDepartment);
            this.groupUserInfo.Controls.Add(this.lblDepartment);
            this.groupUserInfo.Controls.Add(this.cmbRole);
            this.groupUserInfo.Controls.Add(this.lblRole);
            this.groupUserInfo.Controls.Add(this.txtPhone);
            this.groupUserInfo.Controls.Add(this.lblPhone);
            this.groupUserInfo.Controls.Add(this.txtEmail);
            this.groupUserInfo.Controls.Add(this.lblEmail);
            this.groupUserInfo.Controls.Add(this.txtShortName);
            this.groupUserInfo.Controls.Add(this.lblShortName);
            this.groupUserInfo.Controls.Add(this.txtFullName);
            this.groupUserInfo.Controls.Add(this.lblFullName);
            this.groupUserInfo.Controls.Add(this.txtUsername);
            this.groupUserInfo.Controls.Add(this.lblUsername);
            this.groupUserInfo.Location = new System.Drawing.Point(12, 12);
            this.groupUserInfo.Name = "groupUserInfo";
            this.groupUserInfo.Size = new System.Drawing.Size(1073, 212);
            this.groupUserInfo.TabIndex = 0;
            this.groupUserInfo.Text = "User Information";
            // 
            // groupPhoto
            // 
            this.groupPhoto.Controls.Add(this.btnRemovePhoto);
            this.groupPhoto.Controls.Add(this.btnBrowsePhoto);
            this.groupPhoto.Controls.Add(this.pictureUser);
            this.groupPhoto.Location = new System.Drawing.Point(873, 0);
            this.groupPhoto.Name = "groupPhoto";
            this.groupPhoto.Size = new System.Drawing.Size(200, 212);
            this.groupPhoto.TabIndex = 2;
            this.groupPhoto.Text = "User Photo";
            // 
            // btnRemovePhoto
            // 
            this.btnRemovePhoto.Location = new System.Drawing.Point(105, 174);
            this.btnRemovePhoto.Name = "btnRemovePhoto";
            this.btnRemovePhoto.Size = new System.Drawing.Size(80, 23);
            this.btnRemovePhoto.TabIndex = 2;
            this.btnRemovePhoto.Text = "Remove";
            // 
            // btnBrowsePhoto
            // 
            this.btnBrowsePhoto.Location = new System.Drawing.Point(15, 174);
            this.btnBrowsePhoto.Name = "btnBrowsePhoto";
            this.btnBrowsePhoto.Size = new System.Drawing.Size(80, 23);
            this.btnBrowsePhoto.TabIndex = 1;
            this.btnBrowsePhoto.Text = "Browse...";
            // 
            // pictureUser
            // 
            this.pictureUser.Location = new System.Drawing.Point(15, 30);
            this.pictureUser.Name = "pictureUser";
            this.pictureUser.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Auto;
            this.pictureUser.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            this.pictureUser.Size = new System.Drawing.Size(170, 132);
            this.pictureUser.TabIndex = 0;
            // 
            // chkIsActive
            // 
            this.chkIsActive.Location = new System.Drawing.Point(530, 153);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Properties.Caption = "Active User";
            this.chkIsActive.Size = new System.Drawing.Size(75, 19);
            this.chkIsActive.TabIndex = 16;
            // 
            // txtDesignation
            // 
            this.txtDesignation.Location = new System.Drawing.Point(120, 152);
            this.txtDesignation.Name = "txtDesignation";
            this.txtDesignation.Size = new System.Drawing.Size(200, 22);
            this.txtDesignation.TabIndex = 15;
            // 
            // lblDesignation
            // 
            this.lblDesignation.Location = new System.Drawing.Point(20, 155);
            this.lblDesignation.Name = "lblDesignation";
            this.lblDesignation.Size = new System.Drawing.Size(66, 15);
            this.lblDesignation.TabIndex = 14;
            this.lblDesignation.Text = "Designation:";
            // 
            // cmbDepartment
            // 
            this.cmbDepartment.Location = new System.Drawing.Point(530, 122);
            this.cmbDepartment.Name = "cmbDepartment";
            this.cmbDepartment.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbDepartment.Properties.Items.AddRange(new object[] {
            "IT",
            "Finance",
            "HR",
            "Operations",
            "Management"});
            this.cmbDepartment.Size = new System.Drawing.Size(150, 22);
            this.cmbDepartment.TabIndex = 13;
            // 
            // lblDepartment
            // 
            this.lblDepartment.Location = new System.Drawing.Point(450, 125);
            this.lblDepartment.Name = "lblDepartment";
            this.lblDepartment.Size = new System.Drawing.Size(66, 15);
            this.lblDepartment.TabIndex = 12;
            this.lblDepartment.Text = "Department:";
            // 
            // cmbRole
            // 
            this.cmbRole.Location = new System.Drawing.Point(120, 122);
            this.cmbRole.Name = "cmbRole";
            this.cmbRole.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbRole.Properties.Items.AddRange(new object[] {
            "Admin",
            "Manager",
            "User",
            "Viewer"});
            this.cmbRole.Size = new System.Drawing.Size(150, 22);
            this.cmbRole.TabIndex = 11;
            // 
            // lblRole
            // 
            this.lblRole.Location = new System.Drawing.Point(20, 125);
            this.lblRole.Name = "lblRole";
            this.lblRole.Size = new System.Drawing.Size(26, 15);
            this.lblRole.TabIndex = 10;
            this.lblRole.Text = "Role:";
            // 
            // txtPhone
            // 
            this.txtPhone.Location = new System.Drawing.Point(530, 92);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Size = new System.Drawing.Size(150, 22);
            this.txtPhone.TabIndex = 9;
            // 
            // lblPhone
            // 
            this.lblPhone.Location = new System.Drawing.Point(450, 95);
            this.lblPhone.Name = "lblPhone";
            this.lblPhone.Size = new System.Drawing.Size(37, 15);
            this.lblPhone.TabIndex = 8;
            this.lblPhone.Text = "Phone:";
            // 
            // txtEmail
            // 
            this.txtEmail.Location = new System.Drawing.Point(120, 92);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new System.Drawing.Size(300, 22);
            this.txtEmail.TabIndex = 7;
            // 
            // lblEmail
            // 
            this.lblEmail.Location = new System.Drawing.Point(20, 95);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new System.Drawing.Size(32, 15);
            this.lblEmail.TabIndex = 6;
            this.lblEmail.Text = "Email:";
            // 
            // txtShortName
            // 
            this.txtShortName.Location = new System.Drawing.Point(530, 62);
            this.txtShortName.Name = "txtShortName";
            this.txtShortName.Size = new System.Drawing.Size(150, 22);
            this.txtShortName.TabIndex = 5;
            // 
            // lblShortName
            // 
            this.lblShortName.Location = new System.Drawing.Point(450, 65);
            this.lblShortName.Name = "lblShortName";
            this.lblShortName.Size = new System.Drawing.Size(66, 15);
            this.lblShortName.TabIndex = 4;
            this.lblShortName.Text = "Short Name:";
            // 
            // txtFullName
            // 
            this.txtFullName.Location = new System.Drawing.Point(120, 62);
            this.txtFullName.Name = "txtFullName";
            this.txtFullName.Size = new System.Drawing.Size(300, 22);
            this.txtFullName.TabIndex = 3;
            // 
            // lblFullName
            // 
            this.lblFullName.Location = new System.Drawing.Point(20, 65);
            this.lblFullName.Name = "lblFullName";
            this.lblFullName.Size = new System.Drawing.Size(57, 15);
            this.lblFullName.TabIndex = 2;
            this.lblFullName.Text = "Full Name:";
            // 
            // txtUsername
            // 
            this.txtUsername.Location = new System.Drawing.Point(120, 32);
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Size = new System.Drawing.Size(200, 22);
            this.txtUsername.TabIndex = 1;
            // 
            // lblUsername
            // 
            this.lblUsername.Location = new System.Drawing.Point(20, 35);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new System.Drawing.Size(56, 15);
            this.lblUsername.TabIndex = 0;
            this.lblUsername.Text = "Username:";
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.groupGlobalPermissions);
            this.xtraTabPage2.Controls.Add(this.groupActionPermissions);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.Size = new System.Drawing.Size(1088, 397);
            this.xtraTabPage2.Text = "Permissions";
            // 
            // groupGlobalPermissions
            // 
            this.groupGlobalPermissions.Controls.Add(this.chkUserPermOverrides);
            this.groupGlobalPermissions.Location = new System.Drawing.Point(550, 12);
            this.groupGlobalPermissions.Name = "groupGlobalPermissions";
            this.groupGlobalPermissions.Size = new System.Drawing.Size(520, 370);
            this.groupGlobalPermissions.TabIndex = 2;
            this.groupGlobalPermissions.Text = "Global User Management";
            // 
            // groupActionPermissions
            // 
            this.groupActionPermissions.Controls.Add(this.chkPrintAction);
            this.groupActionPermissions.Controls.Add(this.chkExportAction);
            this.groupActionPermissions.Controls.Add(this.chkDeleteAction);
            this.groupActionPermissions.Controls.Add(this.chkUpdateAction);
            this.groupActionPermissions.Controls.Add(this.chkNewAction);
            this.groupActionPermissions.Location = new System.Drawing.Point(12, 12);
            this.groupActionPermissions.Name = "groupActionPermissions";
            this.groupActionPermissions.Size = new System.Drawing.Size(520, 370);
            this.groupActionPermissions.TabIndex = 1;
            this.groupActionPermissions.Text = "Global Action Permissions";
            // 
            // chkPrintAction
            // 
            this.chkPrintAction.Location = new System.Drawing.Point(20, 110);
            this.chkPrintAction.Name = "chkPrintAction";
            this.chkPrintAction.Properties.Caption = "Print";
            this.chkPrintAction.Size = new System.Drawing.Size(75, 19);
            this.chkPrintAction.TabIndex = 5;
            // 
            // chkExportAction
            // 
            this.chkExportAction.Location = new System.Drawing.Point(20, 135);
            this.chkExportAction.Name = "chkExportAction";
            this.chkExportAction.Properties.Caption = "Export";
            this.chkExportAction.Size = new System.Drawing.Size(75, 19);
            this.chkExportAction.TabIndex = 4;
            // 
            // chkDeleteAction
            // 
            this.chkDeleteAction.Location = new System.Drawing.Point(20, 85);
            this.chkDeleteAction.Name = "chkDeleteAction";
            this.chkDeleteAction.Properties.Caption = "Delete";
            this.chkDeleteAction.Size = new System.Drawing.Size(75, 19);
            this.chkDeleteAction.TabIndex = 3;
            // 
            // chkUpdateAction
            // 
            this.chkUpdateAction.Location = new System.Drawing.Point(20, 60);
            this.chkUpdateAction.Name = "chkUpdateAction";
            this.chkUpdateAction.Properties.Caption = "Update/Edit";
            this.chkUpdateAction.Size = new System.Drawing.Size(87, 19);
            this.chkUpdateAction.TabIndex = 2;
            // 
            // chkNewAction
            // 
            this.chkNewAction.Location = new System.Drawing.Point(20, 35);
            this.chkNewAction.Name = "chkNewAction";
            this.chkNewAction.Properties.Caption = "Create";
            this.chkNewAction.Size = new System.Drawing.Size(75, 19);
            this.chkNewAction.TabIndex = 0;
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.groupSystemPreferences);
            this.xtraTabPage3.Controls.Add(this.groupUIPreferences);
            this.xtraTabPage3.Name = "xtraTabPage3";
            this.xtraTabPage3.Size = new System.Drawing.Size(1088, 397);
            this.xtraTabPage3.Text = "Preferences";
            // 
            // groupSystemPreferences
            // 
            this.groupSystemPreferences.Controls.Add(this.chkSoundNotifications);
            this.groupSystemPreferences.Controls.Add(this.chkEmailNotifications);
            this.groupSystemPreferences.Controls.Add(this.cmbTimeFormat);
            this.groupSystemPreferences.Controls.Add(this.lblTimeFormat);
            this.groupSystemPreferences.Controls.Add(this.cmbDateFormat);
            this.groupSystemPreferences.Controls.Add(this.lblDateFormat);
            this.groupSystemPreferences.Location = new System.Drawing.Point(490, 12);
            this.groupSystemPreferences.Name = "groupSystemPreferences";
            this.groupSystemPreferences.Size = new System.Drawing.Size(460, 200);
            this.groupSystemPreferences.TabIndex = 1;
            this.groupSystemPreferences.Text = "System Preferences";
            // 
            // chkSoundNotifications
            // 
            this.chkSoundNotifications.Location = new System.Drawing.Point(20, 125);
            this.chkSoundNotifications.Name = "chkSoundNotifications";
            this.chkSoundNotifications.Properties.Caption = "Sound Notifications";
            this.chkSoundNotifications.Size = new System.Drawing.Size(120, 19);
            this.chkSoundNotifications.TabIndex = 5;
            // 
            // chkEmailNotifications
            // 
            this.chkEmailNotifications.Location = new System.Drawing.Point(20, 95);
            this.chkEmailNotifications.Name = "chkEmailNotifications";
            this.chkEmailNotifications.Properties.Caption = "Email Notifications";
            this.chkEmailNotifications.Size = new System.Drawing.Size(120, 19);
            this.chkEmailNotifications.TabIndex = 4;
            // 
            // cmbTimeFormat
            // 
            this.cmbTimeFormat.Location = new System.Drawing.Point(120, 62);
            this.cmbTimeFormat.Name = "cmbTimeFormat";
            this.cmbTimeFormat.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbTimeFormat.Properties.Items.AddRange(new object[] {
            "12 Hour (AM/PM)",
            "24 Hour"});
            this.cmbTimeFormat.Size = new System.Drawing.Size(150, 22);
            this.cmbTimeFormat.TabIndex = 3;
            // 
            // lblTimeFormat
            // 
            this.lblTimeFormat.Location = new System.Drawing.Point(20, 65);
            this.lblTimeFormat.Name = "lblTimeFormat";
            this.lblTimeFormat.Size = new System.Drawing.Size(71, 15);
            this.lblTimeFormat.TabIndex = 2;
            this.lblTimeFormat.Text = "Time Format:";
            // 
            // cmbDateFormat
            // 
            this.cmbDateFormat.Location = new System.Drawing.Point(120, 32);
            this.cmbDateFormat.Name = "cmbDateFormat";
            this.cmbDateFormat.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbDateFormat.Properties.Items.AddRange(new object[] {
            "dd/MM/yyyy",
            "MM/dd/yyyy",
            "yyyy-MM-dd",
            "dd-MM-yyyy"});
            this.cmbDateFormat.Size = new System.Drawing.Size(150, 22);
            this.cmbDateFormat.TabIndex = 1;
            // 
            // lblDateFormat
            // 
            this.lblDateFormat.Location = new System.Drawing.Point(20, 35);
            this.lblDateFormat.Name = "lblDateFormat";
            this.lblDateFormat.Size = new System.Drawing.Size(68, 15);
            this.lblDateFormat.TabIndex = 0;
            this.lblDateFormat.Text = "Date Format:";
            // 
            // groupUIPreferences
            // 
            this.groupUIPreferences.Controls.Add(this.chkShowNotifications);
            this.groupUIPreferences.Controls.Add(this.chkAutoSave);
            this.groupUIPreferences.Controls.Add(this.cmbLanguage);
            this.groupUIPreferences.Controls.Add(this.lblLanguage);
            this.groupUIPreferences.Controls.Add(this.cmbDefaultTheme);
            this.groupUIPreferences.Controls.Add(this.lblDefaultTheme);
            this.groupUIPreferences.Location = new System.Drawing.Point(12, 12);
            this.groupUIPreferences.Name = "groupUIPreferences";
            this.groupUIPreferences.Size = new System.Drawing.Size(460, 200);
            this.groupUIPreferences.TabIndex = 0;
            this.groupUIPreferences.Text = "User Interface Preferences";
            // 
            // chkShowNotifications
            // 
            this.chkShowNotifications.Location = new System.Drawing.Point(20, 125);
            this.chkShowNotifications.Name = "chkShowNotifications";
            this.chkShowNotifications.Properties.Caption = "Show Notifications";
            this.chkShowNotifications.Size = new System.Drawing.Size(120, 19);
            this.chkShowNotifications.TabIndex = 5;
            // 
            // chkAutoSave
            // 
            this.chkAutoSave.Location = new System.Drawing.Point(20, 95);
            this.chkAutoSave.Name = "chkAutoSave";
            this.chkAutoSave.Properties.Caption = "Auto Save Changes";
            this.chkAutoSave.Size = new System.Drawing.Size(120, 19);
            this.chkAutoSave.TabIndex = 4;
            // 
            // cmbLanguage
            // 
            this.cmbLanguage.Location = new System.Drawing.Point(120, 62);
            this.cmbLanguage.Name = "cmbLanguage";
            this.cmbLanguage.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbLanguage.Properties.Items.AddRange(new object[] {
            "English",
            "Arabic",
            "French",
            "Spanish"});
            this.cmbLanguage.Size = new System.Drawing.Size(150, 22);
            this.cmbLanguage.TabIndex = 3;
            // 
            // lblLanguage
            // 
            this.lblLanguage.Location = new System.Drawing.Point(20, 65);
            this.lblLanguage.Name = "lblLanguage";
            this.lblLanguage.Size = new System.Drawing.Size(55, 15);
            this.lblLanguage.TabIndex = 2;
            this.lblLanguage.Text = "Language:";
            // 
            // cmbDefaultTheme
            // 
            this.cmbDefaultTheme.Location = new System.Drawing.Point(120, 32);
            this.cmbDefaultTheme.Name = "cmbDefaultTheme";
            this.cmbDefaultTheme.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbDefaultTheme.Properties.Items.AddRange(new object[] {
            "Default",
            "Dark",
            "Light",
            "Blue",
            "Office"});
            this.cmbDefaultTheme.Size = new System.Drawing.Size(150, 22);
            this.cmbDefaultTheme.TabIndex = 1;
            // 
            // lblDefaultTheme
            // 
            this.lblDefaultTheme.Location = new System.Drawing.Point(20, 35);
            this.lblDefaultTheme.Name = "lblDefaultTheme";
            this.lblDefaultTheme.Size = new System.Drawing.Size(81, 15);
            this.lblDefaultTheme.TabIndex = 0;
            this.lblDefaultTheme.Text = "Default Theme:";
            // 
            // chkUserPermOverrides
            // 
            this.chkUserPermOverrides.Location = new System.Drawing.Point(27, 35);
            this.chkUserPermOverrides.Name = "chkUserPermOverrides";
            this.chkUserPermOverrides.Properties.Caption = "User Permission Overrides Role";
            this.chkUserPermOverrides.Size = new System.Drawing.Size(188, 19);
            this.chkUserPermOverrides.TabIndex = 1;
            // 
            // UserMasterForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1098, 561);
            this.Controls.Add(this.panelControl1);
            this.Name = "UserMasterForm";
            this.Text = "User Entry";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupSecurity)).EndInit();
            this.groupSecurity.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtEditPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtConfirmPassword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupUserInfo)).EndInit();
            this.groupUserInfo.ResumeLayout(false);
            this.groupUserInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupPhoto)).EndInit();
            this.groupPhoto.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureUser.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDesignation.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDepartment.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbRole.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShortName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFullName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUsername.Properties)).EndInit();
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupGlobalPermissions)).EndInit();
            this.groupGlobalPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupActionPermissions)).EndInit();
            this.groupActionPermissions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintAction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExportAction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDeleteAction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUpdateAction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkNewAction.Properties)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupSystemPreferences)).EndInit();
            this.groupSystemPreferences.ResumeLayout(false);
            this.groupSystemPreferences.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkSoundNotifications.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkEmailNotifications.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTimeFormat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDateFormat.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupUIPreferences)).EndInit();
            this.groupUIPreferences.ResumeLayout(false);
            this.groupUIPreferences.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chkShowNotifications.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkAutoSave.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbLanguage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbDefaultTheme.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkUserPermOverrides.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraEditors.PanelControl panelControl1;

        // MenuRibbon UC Integration - Centralized ribbon control
        private ProManage.Forms.ReusableForms.MenuRibbon menuRibbon;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraEditors.GroupControl groupPhoto;
        private DevExpress.XtraEditors.SimpleButton btnRemovePhoto;
        private DevExpress.XtraEditors.SimpleButton btnBrowsePhoto;
        private DevExpress.XtraEditors.PictureEdit pictureUser;
        private DevExpress.XtraEditors.GroupControl groupSecurity;
        private DevExpress.XtraEditors.TextEdit txtEditPassword;
        private DevExpress.XtraEditors.LabelControl lblEditPassword;
        private DevExpress.XtraEditors.TextEdit txtConfirmPassword;
        private DevExpress.XtraEditors.LabelControl lblConfirmPassword;
        private DevExpress.XtraEditors.TextEdit txtPassword;
        private DevExpress.XtraEditors.LabelControl lblPassword;
        private DevExpress.XtraEditors.GroupControl groupUserInfo;
        private DevExpress.XtraEditors.CheckEdit chkIsActive;
        private DevExpress.XtraEditors.TextEdit txtDesignation;
        private DevExpress.XtraEditors.LabelControl lblDesignation;
        private DevExpress.XtraEditors.ComboBoxEdit cmbDepartment;
        private DevExpress.XtraEditors.LabelControl lblDepartment;
        private DevExpress.XtraEditors.ComboBoxEdit cmbRole;
        private DevExpress.XtraEditors.LabelControl lblRole;
        private DevExpress.XtraEditors.TextEdit txtPhone;
        private DevExpress.XtraEditors.LabelControl lblPhone;
        private DevExpress.XtraEditors.TextEdit txtEmail;
        private DevExpress.XtraEditors.LabelControl lblEmail;
        private DevExpress.XtraEditors.TextEdit txtShortName;
        private DevExpress.XtraEditors.LabelControl lblShortName;
        private DevExpress.XtraEditors.TextEdit txtFullName;
        private DevExpress.XtraEditors.LabelControl lblFullName;
        private DevExpress.XtraEditors.TextEdit txtUsername;
        private DevExpress.XtraEditors.LabelControl lblUsername;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraEditors.GroupControl groupActionPermissions;
        private DevExpress.XtraEditors.CheckEdit chkPrintAction;
        private DevExpress.XtraEditors.CheckEdit chkExportAction;
        private DevExpress.XtraEditors.CheckEdit chkDeleteAction;
        private DevExpress.XtraEditors.CheckEdit chkUpdateAction;
        private DevExpress.XtraEditors.CheckEdit chkNewAction;
        private DevExpress.XtraEditors.GroupControl groupGlobalPermissions;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraEditors.GroupControl groupSystemPreferences;
        private DevExpress.XtraEditors.CheckEdit chkSoundNotifications;
        private DevExpress.XtraEditors.CheckEdit chkEmailNotifications;
        private DevExpress.XtraEditors.ComboBoxEdit cmbTimeFormat;
        private DevExpress.XtraEditors.LabelControl lblTimeFormat;
        private DevExpress.XtraEditors.ComboBoxEdit cmbDateFormat;
        private DevExpress.XtraEditors.LabelControl lblDateFormat;
        private DevExpress.XtraEditors.GroupControl groupUIPreferences;
        private DevExpress.XtraEditors.CheckEdit chkShowNotifications;
        private DevExpress.XtraEditors.CheckEdit chkAutoSave;
        private DevExpress.XtraEditors.ComboBoxEdit cmbLanguage;
        private DevExpress.XtraEditors.LabelControl lblLanguage;
        private DevExpress.XtraEditors.ComboBoxEdit cmbDefaultTheme;
        private DevExpress.XtraEditors.LabelControl lblDefaultTheme;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.CheckEdit chkUserPermOverrides;

        #region MenuRibbon UC Button Aliases for Event Handler Compatibility

        /// <summary>
        /// Provides aliases for MenuRibbon UC buttons to maintain compatibility with existing event handlers
        /// </summary>
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNew => menuRibbon?.BarButtonItemNew;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemEdit => menuRibbon?.BarButtonItemEdit;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemSave => menuRibbon?.BarButtonItemSave;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemCancel => menuRibbon?.BarButtonItemCancel;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemDelete => menuRibbon?.BarButtonItemDelete;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemFirst => menuRibbon?.BarButtonItemFirst;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrevious => menuRibbon?.BarButtonItemPrevious;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNext => menuRibbon?.BarButtonItemNext;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemLast => menuRibbon?.BarButtonItemLast;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrint => menuRibbon?.BarButtonItemPrint;
        public DevExpress.XtraBars.BarButtonItem barButtonItem2 => menuRibbon?.BarButtonItemNew; // Find button mapped to New for compatibility

        #endregion
    }
}