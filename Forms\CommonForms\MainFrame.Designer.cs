namespace ProManage.Forms
{
    partial class MainFrame
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainFrame));
            this.ribbonControl = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.btnEstimate = new DevExpress.XtraBars.BarButtonItem();
            this.btnDatabase = new DevExpress.XtraBars.BarButtonItem();
            this.btnSQLQuery = new DevExpress.XtraBars.BarButtonItem();
            this.btnSettings = new DevExpress.XtraBars.BarButtonItem();
            this.btnLogout = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.btnToggleSidebar = new DevExpress.XtraBars.BarButtonItem();
            this.BtnUserManagement = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPermissions = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPurchase = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSales = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonGroup1 = new DevExpress.XtraBars.BarButtonGroup();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem9 = new DevExpress.XtraBars.BarButtonItem();
            this.btnEstimateReport = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem10 = new DevExpress.XtraBars.BarButtonItem();
            this.btnParams = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem11 = new DevExpress.XtraBars.BarButtonItem();
            this.barLinkContainerItem2 = new DevExpress.XtraBars.BarLinkContainerItem();
            this.barButtonItem12 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem14 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem13 = new DevExpress.XtraBars.BarButtonItem();
            this.barLinkContainerItem1 = new DevExpress.XtraBars.BarLinkContainerItem();
            this.barSubItem2 = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem5 = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem3 = new DevExpress.XtraBars.BarSubItem();
            this.barSubItem4 = new DevExpress.XtraBars.BarSubItem();
            this.barListItem1 = new DevExpress.XtraBars.BarListItem();
            this.btnUsersRoles = new DevExpress.XtraBars.BarSubItem();
            this.btnRoles = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageCategory1 = new DevExpress.XtraBars.Ribbon.RibbonPageCategory();
            this.ribbonPageCategory2 = new DevExpress.XtraBars.Ribbon.RibbonPageCategory();
            this.ribbonPageCategory3 = new DevExpress.XtraBars.Ribbon.RibbonPageCategory();
            this.ribbonPageAdmin = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupAdmin = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup7 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageEstimate = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupEstimate = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup4 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup5 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup6 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage2 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup3 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageDatabase = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupDatabase = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageSystem = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroupSystem = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage4 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup8 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.repositoryItemColorPickEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemColorPickEdit();
            this.repositoryItemProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemProgressBar();
            this.repositoryItemMarqueeProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar();
            this.repositoryItemMemoExEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit();
            this.xtraTabbedMdiManager = new DevExpress.XtraTabbedMdi.XtraTabbedMdiManager(this.components);
            this.statusPanel = new System.Windows.Forms.Panel();
            this.lblLoggedInUser = new System.Windows.Forms.Label();
            this.statusProgressBar = new DevExpress.XtraEditors.MarqueeProgressBarControl();
            this.accordionControl = new DevExpress.XtraBars.Navigation.AccordionControl();
            this.splitContainerControl = new DevExpress.XtraEditors.SplitContainerControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager)).BeginInit();
            this.statusPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.statusProgressBar.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel1)).BeginInit();
            this.splitContainerControl.Panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel2)).BeginInit();
            this.splitContainerControl.SuspendLayout();
            this.SuspendLayout();
            // 
            // ribbonControl
            // 
            this.ribbonControl.ApplicationButtonText = null;
            this.ribbonControl.DrawGroupCaptions = DevExpress.Utils.DefaultBoolean.True;
            this.ribbonControl.ExpandCollapseItem.Id = 0;
            this.ribbonControl.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl.ExpandCollapseItem,
            this.btnEstimate,
            this.btnDatabase,
            this.btnSQLQuery,
            this.btnSettings,
            this.btnLogout,
            this.btnExit,
            this.btnToggleSidebar,
            this.BtnUserManagement,
            this.barBtnPermissions,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barBtnPurchase,
            this.barBtnSales,
            this.barButtonGroup1,
            this.btnEstimateReport,
            this.barButtonItem8,
            this.barButtonItem9,
            this.barButtonItem10,
            this.btnParams,
            this.barSubItem1,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem7,
            this.barButtonItem11,
            this.barLinkContainerItem2,
            this.barButtonItem12,
            this.barButtonItem13,
            this.barButtonItem14,
            this.barLinkContainerItem1,
            this.barListItem1,
            this.barSubItem2,
            this.barSubItem3,
            this.barSubItem4,
            this.barSubItem5,
            this.btnUsersRoles,
            this.btnRoles});
            this.ribbonControl.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.ribbonControl.MaxItemId = 54;
            this.ribbonControl.Name = "ribbonControl";
            this.ribbonControl.PageCategories.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageCategory[] {
            this.ribbonPageCategory1,
            this.ribbonPageCategory2,
            this.ribbonPageCategory3});
            this.ribbonControl.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPageAdmin,
            this.ribbonPage1,
            this.ribbonPageEstimate,
            this.ribbonPage2,
            this.ribbonPageDatabase,
            this.ribbonPageSystem,
            this.ribbonPage4});
            this.ribbonControl.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemColorPickEdit1,
            this.repositoryItemProgressBar1,
            this.repositoryItemMarqueeProgressBar1,
            this.repositoryItemMemoExEdit1});
            this.ribbonControl.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.OfficeUniversal;
            this.ribbonControl.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.ribbonControl.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Show;
            this.ribbonControl.ShowToolbarCustomizeItem = false;
            this.ribbonControl.Size = new System.Drawing.Size(1221, 115);
            this.ribbonControl.Toolbar.ShowCustomizeItem = false;
            this.ribbonControl.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
            // 
            // btnEstimate
            // 
            this.btnEstimate.ActAsDropDown = true;
            this.btnEstimate.AllowAllUp = true;
            this.btnEstimate.AllowDrawArrowInMenu = false;
            this.btnEstimate.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.btnEstimate.Caption = "Estimates";
            this.btnEstimate.DropDownEnabled = false;
            this.btnEstimate.Id = 1;
            this.btnEstimate.Name = "btnEstimate";
            this.btnEstimate.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnEstimate_ItemClick);
            // 
            // btnDatabase
            // 
            this.btnDatabase.Caption = "Database";
            this.btnDatabase.Id = 2;
            this.btnDatabase.Name = "btnDatabase";
            this.btnDatabase.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnDatabase_ItemClick);
            // 
            // btnSQLQuery
            // 
            this.btnSQLQuery.Caption = "SQL Query";
            this.btnSQLQuery.Id = 9;
            this.btnSQLQuery.Name = "btnSQLQuery";
            this.btnSQLQuery.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnSQLQuery_ItemClick);
            // 
            // btnSettings
            // 
            this.btnSettings.Caption = "Settings";
            this.btnSettings.Id = 3;
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnSettings_ItemClick);
            // 
            // btnLogout
            // 
            this.btnLogout.Caption = "Logout";
            this.btnLogout.Id = 4;
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnLogout_ItemClick);
            // 
            // btnExit
            // 
            this.btnExit.Caption = "Exit";
            this.btnExit.Id = 5;
            this.btnExit.Name = "btnExit";
            this.btnExit.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnExit_ItemClick);
            // 
            // btnToggleSidebar
            // 
            this.btnToggleSidebar.Caption = "Toggle Sidebar";
            this.btnToggleSidebar.Hint = "Toggle sidebar visibility";
            this.btnToggleSidebar.Id = 8;
            this.btnToggleSidebar.Name = "btnToggleSidebar";
            this.btnToggleSidebar.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnToggleSidebar_ItemClick);
            // 
            // BtnUserManagement
            // 
            this.BtnUserManagement.Caption = "User Management";
            this.BtnUserManagement.Id = 14;
            this.BtnUserManagement.Name = "BtnUserManagement";
            this.BtnUserManagement.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnUserManagement_ItemClick);
            // 
            // barBtnPermissions
            // 
            this.barBtnPermissions.ActAsDropDown = true;
            this.barBtnPermissions.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnPermissions.Caption = "Role & Permission Management";
            this.barBtnPermissions.Id = 15;
            this.barBtnPermissions.Name = "barBtnPermissions";
            this.barBtnPermissions.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnRoleManagement_ItemClick);
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "About";
            this.barButtonItem1.Id = 16;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "Refresh Cache";
            this.barButtonItem2.Id = 17;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Help";
            this.barButtonItem3.Id = 18;
            this.barButtonItem3.Name = "barButtonItem3";
            // 
            // barBtnPurchase
            // 
            this.barBtnPurchase.Caption = "Purchase";
            this.barBtnPurchase.Id = 18;
            this.barBtnPurchase.Name = "barBtnPurchase";
            // 
            // barBtnSales
            // 
            this.barBtnSales.Caption = "Sales";
            this.barBtnSales.Id = 19;
            this.barBtnSales.Name = "barBtnSales";
            // 
            // barButtonGroup1
            // 
            this.barButtonGroup1.Caption = "Transaction Options";
            this.barButtonGroup1.Id = 20;
            this.barButtonGroup1.ItemLinks.Add(this.barButtonItem8);
            this.barButtonGroup1.ItemLinks.Add(this.barButtonItem9);
            this.barButtonGroup1.Name = "barButtonGroup1";
            // 
            // barButtonItem8
            // 
            this.barButtonItem8.Caption = "Option 1";
            this.barButtonItem8.Id = 23;
            this.barButtonItem8.Name = "barButtonItem8";
            // 
            // barButtonItem9
            // 
            this.barButtonItem9.Caption = "Option 2";
            this.barButtonItem9.Id = 24;
            this.barButtonItem9.Name = "barButtonItem9";
            //            // btnEstimateReport
            // 
            this.btnEstimateReport.Caption = "Reports";
            this.btnEstimateReport.Id = 21;
            this.btnEstimateReport.Name = "btnEstimateReport";
            this.btnEstimateReport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnEstimateReport_ItemClick);
            // 
            // barButtonItem10
            // 
            this.barButtonItem10.Caption = "Test Form";
            this.barButtonItem10.Id = 25;
            this.barButtonItem10.Name = "barButtonItem10";
            this.barButtonItem10.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnTestForm_ItemClick);
            // 
            // btnParams
            // 
            this.btnParams.Caption = "Parameters";
            this.btnParams.Id = 32;
            this.btnParams.Name = "btnParams";
            this.btnParams.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.BtnParams_ItemClick);
            // 
            // barSubItem1
            // 
            this.barSubItem1.Caption = "Estimate";
            this.barSubItem1.Id = 33;
            this.barSubItem1.Name = "barSubItem1";
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "barButtonItem4";
            this.barButtonItem4.Id = 34;
            this.barButtonItem4.Name = "barButtonItem4";
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "barButtonItem5";
            this.barButtonItem5.Id = 35;
            this.barButtonItem5.Name = "barButtonItem5";
            // 
            // barButtonItem7
            // 
            this.barButtonItem7.Caption = "barButtonItem7";
            this.barButtonItem7.Id = 36;
            this.barButtonItem7.Name = "barButtonItem7";
            // 
            // barButtonItem11
            // 
            this.barButtonItem11.Caption = "Sub1";
            this.barButtonItem11.Id = 39;
            this.barButtonItem11.Name = "barButtonItem11";
            // 
            // barLinkContainerItem2
            // 
            this.barLinkContainerItem2.Caption = "Sub2";
            this.barLinkContainerItem2.Id = 40;
            this.barLinkContainerItem2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem12),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem14)});
            this.barLinkContainerItem2.Name = "barLinkContainerItem2";
            // 
            // barButtonItem12
            // 
            this.barButtonItem12.Caption = "subsub1";
            this.barButtonItem12.Id = 42;
            this.barButtonItem12.Name = "barButtonItem12";
            // 
            // barButtonItem14
            // 
            this.barButtonItem14.Caption = "subsub2";
            this.barButtonItem14.Id = 44;
            this.barButtonItem14.Name = "barButtonItem14";
            // 
            // barButtonItem13
            // 
            this.barButtonItem13.Caption = "Sub3";
            this.barButtonItem13.Id = 43;
            this.barButtonItem13.Name = "barButtonItem13";
            // 
            // barLinkContainerItem1
            // 
            this.barLinkContainerItem1.Caption = "barLinkContainerItem1";
            this.barLinkContainerItem1.Id = 46;
            this.barLinkContainerItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem2),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem3)});
            this.barLinkContainerItem1.Name = "barLinkContainerItem1";
            // 
            // barSubItem2
            // 
            this.barSubItem2.Caption = "barSubItem2";
            this.barSubItem2.Id = 48;
            this.barSubItem2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem5)});
            this.barSubItem2.Name = "barSubItem2";
            // 
            // barSubItem5
            // 
            this.barSubItem5.Caption = "barSubItem5";
            this.barSubItem5.Id = 51;
            this.barSubItem5.Name = "barSubItem5";
            // 
            // barSubItem3
            // 
            this.barSubItem3.Caption = "barSubItem3";
            this.barSubItem3.Id = 49;
            this.barSubItem3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem4)});
            this.barSubItem3.Name = "barSubItem3";
            // 
            // barSubItem4
            // 
            this.barSubItem4.Caption = "barSubItem4";
            this.barSubItem4.Id = 50;
            this.barSubItem4.Name = "barSubItem4";
            // 
            // barListItem1
            // 
            this.barListItem1.Caption = "barListItem1";
            this.barListItem1.Id = 47;
            this.barListItem1.Name = "barListItem1";
            // 
            // btnUsersRoles
            // 
            this.btnUsersRoles.Caption = "Users && Roles";
            this.btnUsersRoles.Id = 52;
            this.btnUsersRoles.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.BtnUserManagement),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnRoles)});
            this.btnUsersRoles.Name = "btnUsersRoles";
            // 
            // btnRoles
            // 
            this.btnRoles.Caption = "Roles && Permission Management";
            this.btnRoles.Id = 53;
            this.btnRoles.Name = "btnRoles";
            this.btnRoles.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRoles_ItemClick);
            // 
            // ribbonPageCategory1
            // 
            this.ribbonPageCategory1.Name = "ribbonPageCategory1";
            // 
            // ribbonPageCategory2
            // 
            this.ribbonPageCategory2.Name = "ribbonPageCategory2";
            this.ribbonPageCategory2.Text = "ribbonPageCategory2";
            // 
            // ribbonPageCategory3
            // 
            this.ribbonPageCategory3.Name = "ribbonPageCategory3";
            this.ribbonPageCategory3.Text = "ribbonPageCategory3";
            // 
            // ribbonPageAdmin
            // 
            this.ribbonPageAdmin.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupAdmin,
            this.ribbonPageGroup7});
            this.ribbonPageAdmin.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPageAdmin.ImageOptions.SvgImage")));
            this.ribbonPageAdmin.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPageAdmin.Name = "ribbonPageAdmin";
            this.ribbonPageAdmin.Text = "Administration";
            // 
            // ribbonPageGroupAdmin
            // 
            this.ribbonPageGroupAdmin.ItemLinks.Add(this.btnUsersRoles);
            this.ribbonPageGroupAdmin.Name = "ribbonPageGroupAdmin";
            this.ribbonPageGroupAdmin.Text = "User Management";
            // 
            // ribbonPageGroup7
            // 
            this.ribbonPageGroup7.ItemLinks.Add(this.btnParams, true);
            this.ribbonPageGroup7.Name = "ribbonPageGroup7";
            this.ribbonPageGroup7.Text = "System";
            // 
            // ribbonPage1
            // 
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1});
            this.ribbonPage1.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPage1.ImageOptions.SvgImage")));
            this.ribbonPage1.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPage1.Name = "ribbonPage1";
            this.ribbonPage1.Text = "Master";
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.barButtonItem10);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            // 
            // ribbonPageEstimate
            // 
            this.ribbonPageEstimate.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupEstimate,
            this.ribbonPageGroup4,
            this.ribbonPageGroup5,
            this.ribbonPageGroup6});
            this.ribbonPageEstimate.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPageEstimate.ImageOptions.SvgImage")));
            this.ribbonPageEstimate.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPageEstimate.Name = "ribbonPageEstimate";
            this.ribbonPageEstimate.Text = "Transactions";
            // 
            // ribbonPageGroupEstimate
            // 
            this.ribbonPageGroupEstimate.ItemLinks.Add(this.btnEstimate);
            this.ribbonPageGroupEstimate.Name = "ribbonPageGroupEstimate";
            // 
            // ribbonPageGroup4
            // 
            this.ribbonPageGroup4.ItemLinks.Add(this.barBtnPurchase);
            this.ribbonPageGroup4.Name = "ribbonPageGroup4";
            // 
            // ribbonPageGroup5
            // 
            this.ribbonPageGroup5.ItemLinks.Add(this.barBtnSales);
            this.ribbonPageGroup5.Name = "ribbonPageGroup5";
            // 
            // ribbonPageGroup6
            // 
            this.ribbonPageGroup6.ItemLinks.Add(this.barLinkContainerItem1);
            this.ribbonPageGroup6.Name = "ribbonPageGroup6";
            this.ribbonPageGroup6.Text = "ribbonPageGroup6";
            // 
            // ribbonPage2
            // 
            this.ribbonPage2.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup3});
            this.ribbonPage2.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPage2.ImageOptions.SvgImage")));
            this.ribbonPage2.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPage2.Name = "ribbonPage2";
            this.ribbonPage2.Text = "Reports";
            // 
            // ribbonPageGroup3
            // 
            this.ribbonPageGroup3.ItemLinks.Add(this.btnEstimateReport);
            this.ribbonPageGroup3.Name = "ribbonPageGroup3";
            this.ribbonPageGroup3.Text = "Standard Reports";
            // 
            // ribbonPageDatabase
            // 
            this.ribbonPageDatabase.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupDatabase});
            this.ribbonPageDatabase.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPageDatabase.ImageOptions.SvgImage")));
            this.ribbonPageDatabase.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPageDatabase.Name = "ribbonPageDatabase";
            this.ribbonPageDatabase.Text = "Database";
            // 
            // ribbonPageGroupDatabase
            // 
            this.ribbonPageGroupDatabase.ItemLinks.Add(this.btnDatabase);
            this.ribbonPageGroupDatabase.ItemLinks.Add(this.btnSQLQuery, true);
            this.ribbonPageGroupDatabase.Name = "ribbonPageGroupDatabase";
            this.ribbonPageGroupDatabase.Text = "Database Tools";
            // 
            // ribbonPageSystem
            // 
            this.ribbonPageSystem.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupSystem,
            this.ribbonPageGroup2});
            this.ribbonPageSystem.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPageSystem.ImageOptions.SvgImage")));
            this.ribbonPageSystem.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPageSystem.Name = "ribbonPageSystem";
            this.ribbonPageSystem.Text = "Settings";
            // 
            // ribbonPageGroupSystem
            // 
            this.ribbonPageGroupSystem.ItemLinks.Add(this.btnToggleSidebar);
            this.ribbonPageGroupSystem.ItemLinks.Add(this.btnSettings, true);
            this.ribbonPageGroupSystem.ItemLinks.Add(this.barButtonItem1, true);
            this.ribbonPageGroupSystem.ItemLinks.Add(this.barButtonItem3, true);
            this.ribbonPageGroupSystem.ItemLinks.Add(this.barButtonItem2, true);
            this.ribbonPageGroupSystem.ItemLinks.Add(this.btnLogout, true);
            this.ribbonPageGroupSystem.Name = "ribbonPageGroupSystem";
            this.ribbonPageGroupSystem.Text = "System Tools";
            // 
            // ribbonPageGroup2
            // 
            this.ribbonPageGroup2.ItemLinks.Add(this.btnExit);
            this.ribbonPageGroup2.Name = "ribbonPageGroup2";
            this.ribbonPageGroup2.Text = "Exit";
            // 
            // ribbonPage4
            // 
            this.ribbonPage4.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup8});
            this.ribbonPage4.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("ribbonPage4.ImageOptions.SvgImage")));
            this.ribbonPage4.ImageOptions.SvgImageSize = new System.Drawing.Size(15, 15);
            this.ribbonPage4.Name = "ribbonPage4";
            this.ribbonPage4.Text = "Utilities";
            // 
            // ribbonPageGroup8
            // 
            this.ribbonPageGroup8.Name = "ribbonPageGroup8";
            this.ribbonPageGroup8.Text = "Tools";
            // 
            // repositoryItemColorPickEdit1
            // 
            this.repositoryItemColorPickEdit1.AutomaticColor = System.Drawing.Color.Black;
            this.repositoryItemColorPickEdit1.Name = "repositoryItemColorPickEdit1";
            // 
            // repositoryItemProgressBar1
            // 
            this.repositoryItemProgressBar1.Name = "repositoryItemProgressBar1";
            // 
            // repositoryItemMarqueeProgressBar1
            // 
            this.repositoryItemMarqueeProgressBar1.Name = "repositoryItemMarqueeProgressBar1";
            // 
            // repositoryItemMemoExEdit1
            // 
            this.repositoryItemMemoExEdit1.AutoHeight = false;
            this.repositoryItemMemoExEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemMemoExEdit1.Name = "repositoryItemMemoExEdit1";
            // 
            // xtraTabbedMdiManager
            // 
            this.xtraTabbedMdiManager.AppearancePage.Header.Options.UseFont = true;
            this.xtraTabbedMdiManager.AppearancePage.HeaderActive.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.xtraTabbedMdiManager.AppearancePage.HeaderActive.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.xtraTabbedMdiManager.AppearancePage.HeaderActive.Options.UseFont = true;
            this.xtraTabbedMdiManager.AppearancePage.HeaderActive.Options.UseForeColor = true;
            this.xtraTabbedMdiManager.ClosePageButtonShowMode = DevExpress.XtraTab.ClosePageButtonShowMode.InActiveTabPageAndTabControlHeader;
            this.xtraTabbedMdiManager.FloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
            this.xtraTabbedMdiManager.FloatOnDrag = DevExpress.Utils.DefaultBoolean.True;
            this.xtraTabbedMdiManager.HeaderButtons = DevExpress.XtraTab.TabButtons.Close;
            this.xtraTabbedMdiManager.HeaderButtonsShowMode = DevExpress.XtraTab.TabButtonShowMode.Always;
            this.xtraTabbedMdiManager.MdiParent = this;
            this.xtraTabbedMdiManager.UseFormIconAsPageImage = DevExpress.Utils.DefaultBoolean.True;
            // 
            // statusPanel
            // 
            this.statusPanel.Controls.Add(this.lblLoggedInUser);
            this.statusPanel.Controls.Add(this.statusProgressBar);
            this.statusPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.statusPanel.Location = new System.Drawing.Point(0, 548);
            this.statusPanel.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.statusPanel.Name = "statusPanel";
            this.statusPanel.Size = new System.Drawing.Size(1221, 25);
            this.statusPanel.TabIndex = 4;
            // 
            // lblLoggedInUser
            // 
            this.lblLoggedInUser.AutoSize = true;
            this.lblLoggedInUser.Location = new System.Drawing.Point(12, 5);
            this.lblLoggedInUser.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.lblLoggedInUser.Name = "lblLoggedInUser";
            this.lblLoggedInUser.Size = new System.Drawing.Size(161, 15);
            this.lblLoggedInUser.TabIndex = 0;
            this.lblLoggedInUser.Text = "Logged in as: [Not logged in]";
            // 
            // statusProgressBar
            // 
            this.statusProgressBar.Anchor = System.Windows.Forms.AnchorStyles.Right;
            this.statusProgressBar.EditValue = 0;
            this.statusProgressBar.Location = new System.Drawing.Point(988, 3);
            this.statusProgressBar.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.statusProgressBar.Name = "statusProgressBar";
            this.statusProgressBar.Properties.Appearance.BackColor = System.Drawing.SystemColors.Control;
            this.statusProgressBar.Properties.MarqueeAnimationSpeed = 50;
            this.statusProgressBar.Size = new System.Drawing.Size(222, 18);
            this.statusProgressBar.TabIndex = 0;
            this.statusProgressBar.Visible = false;
            // 
            // accordionControl
            // 
            this.accordionControl.Dock = System.Windows.Forms.DockStyle.Left;
            this.accordionControl.Location = new System.Drawing.Point(0, 0);
            this.accordionControl.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.accordionControl.Name = "accordionControl";
            this.accordionControl.OptionsMinimizing.MinimizedWidth = 48;
            this.accordionControl.OptionsMinimizing.State = DevExpress.XtraBars.Navigation.AccordionControlState.Minimized;
            this.accordionControl.ScrollBarMode = DevExpress.XtraBars.Navigation.ScrollBarMode.Touch;
            this.accordionControl.Size = new System.Drawing.Size(48, 433);
            this.accordionControl.TabIndex = 0;
            this.accordionControl.ViewType = DevExpress.XtraBars.Navigation.AccordionControlViewType.HamburgerMenu;
            // 
            // splitContainerControl
            // 
            this.splitContainerControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainerControl.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl.IsSplitterFixed = true;
            this.splitContainerControl.Location = new System.Drawing.Point(0, 115);
            this.splitContainerControl.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.splitContainerControl.Name = "splitContainerControl";
            // 
            // splitContainerControl.Panel1
            // 
            this.splitContainerControl.Panel1.Controls.Add(this.accordionControl);
            this.splitContainerControl.Panel1.MinSize = 30;
            this.splitContainerControl.Panel1.Text = "Panel1";
            // 
            // splitContainerControl.Panel2
            // 
            this.splitContainerControl.Panel2.AutoScroll = true;
            this.splitContainerControl.Panel2.Text = "Panel2";
            this.splitContainerControl.Size = new System.Drawing.Size(1221, 433);
            this.splitContainerControl.SplitterPosition = 48;
            this.splitContainerControl.TabIndex = 3;
            // 
            // MainFrame
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1221, 573);
            this.Controls.Add(this.splitContainerControl);
            this.Controls.Add(this.statusPanel);
            this.Controls.Add(this.ribbonControl);
            this.IsMdiContainer = true;
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "MainFrame";
            this.Ribbon = this.ribbonControl;
            this.Text = "ProManage";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.MainFrame_Load);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemColorPickEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoExEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager)).EndInit();
            this.statusPanel.ResumeLayout(false);
            this.statusPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.statusProgressBar.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accordionControl)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel1)).EndInit();
            this.splitContainerControl.Panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl.Panel2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl)).EndInit();
            this.splitContainerControl.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel statusPanel;
        private System.Windows.Forms.Label lblLoggedInUser;
        private DevExpress.XtraEditors.MarqueeProgressBarControl statusProgressBar;

        // Ribbon Control
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl;
        private DevExpress.XtraBars.BarButtonItem btnEstimate;
        private DevExpress.XtraBars.BarButtonItem btnDatabase;
        private DevExpress.XtraBars.BarButtonItem btnSQLQuery;
        private DevExpress.XtraBars.BarButtonItem btnSettings;
        private DevExpress.XtraBars.BarButtonItem btnLogout;
        private DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarButtonItem btnToggleSidebar;
        private DevExpress.XtraBars.BarButtonItem BtnUserManagement;
        private DevExpress.XtraBars.BarButtonItem barBtnPermissions;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barBtnPurchase;
        private DevExpress.XtraBars.BarButtonItem barBtnSales;
        private DevExpress.XtraBars.BarButtonGroup barButtonGroup1;
        private DevExpress.XtraBars.BarButtonItem btnEstimateReport;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraBars.BarButtonItem barButtonItem9;
        private DevExpress.XtraBars.BarButtonItem barButtonItem10;

        // Ribbon Pages and Groups
        private DevExpress.XtraBars.Ribbon.RibbonPageCategory ribbonPageCategory1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageAdmin;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupAdmin;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup7;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage1;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageEstimate;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupEstimate;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup4;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup5;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage2;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup3;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageDatabase;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupDatabase;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageSystem;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupSystem;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup2;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage4;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup8;

        // Repository Items
        private DevExpress.XtraEditors.Repository.RepositoryItemColorPickEdit repositoryItemColorPickEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemProgressBar repositoryItemProgressBar1;
        private DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar repositoryItemMarqueeProgressBar1;

        // XtraTabbedMdiManager
        private DevExpress.XtraTabbedMdi.XtraTabbedMdiManager xtraTabbedMdiManager;
        private DevExpress.XtraBars.BarButtonItem btnParams;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup6;
        private DevExpress.XtraBars.BarButtonItem barButtonItem11;
        private DevExpress.XtraBars.BarLinkContainerItem barLinkContainerItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem12;
        private DevExpress.XtraBars.BarButtonItem barButtonItem13;
        private DevExpress.XtraBars.BarButtonItem barButtonItem14;
        private DevExpress.XtraBars.BarLinkContainerItem barLinkContainerItem1;
        private DevExpress.XtraBars.BarSubItem barSubItem2;
        private DevExpress.XtraBars.BarSubItem barSubItem5;
        private DevExpress.XtraBars.BarSubItem barSubItem3;
        private DevExpress.XtraBars.BarSubItem barSubItem4;
        private DevExpress.XtraBars.BarListItem barListItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoExEdit repositoryItemMemoExEdit1;
        private DevExpress.XtraBars.Ribbon.RibbonPageCategory ribbonPageCategory2;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl;
        private DevExpress.XtraBars.Navigation.AccordionControl accordionControl;
        private DevExpress.XtraBars.Ribbon.RibbonPageCategory ribbonPageCategory3;
        private DevExpress.XtraBars.BarSubItem btnUsersRoles;
        private DevExpress.XtraBars.BarButtonItem btnRoles;
    }
}
