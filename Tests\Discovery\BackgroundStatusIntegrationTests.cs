using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Integration tests for background status functionality
    /// Task 7: Integration Testing and Manual Validation
    /// </summary>
    [TestClass]
    public class BackgroundStatusIntegrationTests
    {
        #region Test Setup and Cleanup

        [TestInitialize]
        public void TestInitialize()
        {
            // Clear status history before each test
            StatusProgressService.ClearHistory();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Clean up after tests
            StatusProgressService.ClearHistory();
        }

        #endregion

        #region End-to-End Status Flow Tests

        [TestMethod]
        public async Task EndToEnd_FormDiscoveryWithStatusUpdates_CompletesSuccessfully()
        {
            // Arrange
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => 
            {
                statusUpdates.Add(status);
                Console.WriteLine($"Status Update: {status}");
            });

            // Act
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Verify expected status progression
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("Initializing")));
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("complete") || s.Contains("failed")));
            
            // Verify status history was recorded
            var history = StatusProgressService.GetRecentHistory(10);
            Assert.IsTrue(history.Count > 0);
        }

        [TestMethod]
        public async Task EndToEnd_ConcurrentDiscoveryOperations_HandleStatusCorrectly()
        {
            // Arrange
            var statusUpdates1 = new List<string>();
            var statusUpdates2 = new List<string>();
            var progress1 = new Progress<string>(status => statusUpdates1.Add(status));
            var progress2 = new Progress<string>(status => statusUpdates2.Add(status));

            // Act - Run two discovery operations concurrently
            var task1 = Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress1));
            var task2 = Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress2));

            var results = await Task.WhenAll(task1, task2);

            // Assert
            Assert.IsNotNull(results[0]);
            Assert.IsNotNull(results[1]);
            Assert.IsTrue(statusUpdates1.Count > 0);
            Assert.IsTrue(statusUpdates2.Count > 0);
            
            // Both operations should complete successfully
            Assert.IsTrue(statusUpdates1.Exists(s => s.Contains("complete") || s.Contains("cache")));
            Assert.IsTrue(statusUpdates2.Exists(s => s.Contains("complete") || s.Contains("cache")));
        }

        #endregion

        #region UI Thread Safety Tests

        [TestMethod]
        public async Task UIThreadSafety_ProgressReporting_WorksCorrectly()
        {
            // Arrange
            var statusUpdates = new List<string>();
            var uiThreadId = Thread.CurrentThread.ManagedThreadId;
            var progressThreadIds = new List<int>();

            var progress = new Progress<string>(status => 
            {
                statusUpdates.Add(status);
                progressThreadIds.Add(Thread.CurrentThread.ManagedThreadId);
            });

            // Act
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));

            // Allow time for progress updates to be processed
            await Task.Delay(500);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Progress callbacks should run on UI thread (same as test thread in this case)
            Assert.IsTrue(progressThreadIds.TrueForAll(id => id == uiThreadId));
        }

        [TestMethod]
        public void UIThreadSafety_StatusProgressService_ThreadSafe()
        {
            // Arrange
            var tasks = new List<Task>();
            var statusCount = 0;

            // Act - Add status from multiple threads
            for (int i = 0; i < 10; i++)
            {
                var index = i;
                tasks.Add(Task.Run(() =>
                {
                    StatusProgressService.AddToHistory($"Status {index}", $"Operation {index}");
                    Interlocked.Increment(ref statusCount);
                }));
            }

            Task.WaitAll(tasks.ToArray());

            // Assert
            Assert.AreEqual(10, statusCount);
            var history = StatusProgressService.GetRecentHistory(20);
            Assert.AreEqual(10, history.Count);
        }

        #endregion

        #region Error Scenario Tests

        [TestMethod]
        public async Task ErrorScenario_ProgressReporterThrows_OperationContinues()
        {
            // Arrange
            var callCount = 0;
            var progress = new Progress<string>(status => 
            {
                callCount++;
                if (callCount == 2) // Throw on second call
                    throw new InvalidOperationException("Test exception");
            });

            // Act & Assert - Should not throw exception
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));
            Assert.IsNotNull(result);
            Assert.IsTrue(callCount >= 2);
        }

        [TestMethod]
        public async Task ErrorScenario_TimeoutHandling_ReportsCorrectStatus()
        {
            // This test would require mocking to force a timeout
            // For now, we'll test that the timeout mechanism exists
            
            // Arrange
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => statusUpdates.Add(status));

            // Act
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));

            // Assert
            Assert.IsNotNull(result);
            // The operation should complete without timeout in normal circumstances
            Assert.IsFalse(statusUpdates.Exists(s => s.Contains("timed out")));
        }

        #endregion

        #region Performance Tests

        [TestMethod]
        public async Task Performance_StatusReporting_DoesNotSignificantlySlowOperation()
        {
            // Arrange
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => statusUpdates.Add(status));

            // Act - Measure time with status reporting
            var startTime = DateTime.Now;
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));
            var timeWithStatus = DateTime.Now - startTime;

            // Act - Measure time without status reporting
            startTime = DateTime.Now;
            var result2 = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(null));
            var timeWithoutStatus = DateTime.Now - startTime;

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result2);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Status reporting should not add more than 50% overhead
            Assert.IsTrue(timeWithStatus.TotalMilliseconds < timeWithoutStatus.TotalMilliseconds * 1.5);
        }

        [TestMethod]
        public void Performance_StatusHistory_HandlesLargeVolume()
        {
            // Arrange & Act
            var startTime = DateTime.Now;
            
            for (int i = 0; i < 1000; i++)
            {
                StatusProgressService.AddToHistory($"Status {i}", $"Operation {i}");
            }
            
            var elapsed = DateTime.Now - startTime;

            // Assert
            Assert.IsTrue(elapsed.TotalSeconds < 1); // Should complete in under 1 second
            
            var history = StatusProgressService.GetRecentHistory(100);
            Assert.AreEqual(50, history.Count); // Should be limited to MaxHistoryEntries
        }

        #endregion

        #region Real-World Scenario Tests

        [TestMethod]
        public async Task RealWorld_FormLoadScenario_StatusUpdatesWork()
        {
            // Simulate the form load scenario
            // Arrange
            var statusUpdates = new List<string>();
            var progress = new Progress<string>(status => 
            {
                statusUpdates.Add(status);
                // Simulate UI update delay
                Thread.Sleep(10);
            });

            // Act - Simulate form load with discovery
            var discoveryTask = Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));

            // Simulate other form initialization happening concurrently
            var otherInitTask = Task.Run(async () =>
            {
                await Task.Delay(100); // Simulate other initialization work
                return "Other init complete";
            });

            // Wait for both tasks to complete
            var discoveryResult = await discoveryTask;
            var otherResult = await otherInitTask;

            // Assert
            Assert.IsNotNull(discoveryResult); // Discovery result
            Assert.AreEqual("Other init complete", otherResult);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Should have progression of status updates
            var hasInitializing = statusUpdates.Exists(s => s.Contains("Initializing"));
            var hasCompletion = statusUpdates.Exists(s => s.Contains("complete") || s.Contains("sync"));
            Assert.IsTrue(hasInitializing);
            Assert.IsTrue(hasCompletion);
        }

        [TestMethod]
        public async Task RealWorld_RefreshButtonScenario_StatusUpdatesWork()
        {
            // Simulate the refresh button click scenario
            // Arrange
            var statusUpdates = new List<string>();
            IProgress<string> progress = new Progress<string>(status => statusUpdates.Add(status));

            // Act - Simulate refresh operation
            var refreshTask = Task.Run(async () =>
            {
                // Simulate checking for concurrent operations
                await Task.Delay(50);
                progress.Report(StatusProgressService.SyncMessages.CheckingConcurrent);

                // Simulate starting sync
                await Task.Delay(50);
                progress.Report(StatusProgressService.SyncMessages.Starting);

                // Simulate discovery
                var discoveryResult = FormDiscoveryService.CompareFormsWithDatabase(progress);

                // Simulate completion
                await Task.Delay(50);
                progress.Report(StatusProgressService.SyncMessages.Completed);

                return discoveryResult;
            });

            var result = await refreshTask;

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(statusUpdates.Count > 0);
            
            // Should have expected status progression
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("Checking")));
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("Starting") || s.Contains("Initializing")));
            Assert.IsTrue(statusUpdates.Exists(s => s.Contains("completed")));
        }

        #endregion

        #region Data Validation Tests

        [TestMethod]
        public async Task DataValidation_StatusHistory_ContainsAccurateInformation()
        {
            // Arrange
            var statusUpdates = new List<string>();
            IProgress<string> progress = new Progress<string>(status => statusUpdates.Add(status));

            // Act
            var startTime = DateTime.Now;
            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));
            var endTime = DateTime.Now;

            // Assert
            Assert.IsNotNull(result);
            
            var history = StatusProgressService.GetRecentHistory(10);
            Assert.IsTrue(history.Count > 0);
            
            // Verify history entries have reasonable timestamps
            foreach (var entry in history)
            {
                Assert.IsTrue(entry.Timestamp >= startTime);
                Assert.IsTrue(entry.Timestamp <= endTime.AddSeconds(1)); // Allow small buffer
                Assert.IsFalse(string.IsNullOrEmpty(entry.Status));
            }
        }

        [TestMethod]
        public void DataValidation_StatusMessages_AreUserFriendly()
        {
            // Test that status messages are user-friendly and informative
            var messages = new[]
            {
                StatusProgressService.FormDiscoveryMessages.Initializing,
                StatusProgressService.FormDiscoveryMessages.CheckingCache,
                StatusProgressService.FormDiscoveryMessages.ScanningForms,
                StatusProgressService.FormDiscoveryMessages.CheckingDatabase,
                StatusProgressService.FormDiscoveryMessages.ComparingForms,
                StatusProgressService.FormDiscoveryMessages.UpdatingCache,
                StatusProgressService.FormDiscoveryMessages.UsingCache,
                StatusProgressService.FormDiscoveryMessages.DiscoveryComplete,
                StatusProgressService.SyncMessages.CheckingConcurrent,
                StatusProgressService.SyncMessages.Starting,
                StatusProgressService.SyncMessages.Completed
            };

            foreach (var message in messages)
            {
                Assert.IsFalse(string.IsNullOrEmpty(message));
                Assert.IsTrue(message.Length > 5); // Should be descriptive
                Assert.IsFalse(message.Contains("Exception")); // Should not contain technical errors
                Assert.IsFalse(message.Contains("null")); // Should not contain null references
            }
        }

        #endregion

        #region Manual Testing Helpers

        /// <summary>
        /// Helper method for manual testing - prints all status updates
        /// </summary>
        public static async Task<FormSyncResult> ManualTestFormDiscovery()
        {
            Console.WriteLine("=== Manual Test: Form Discovery with Status Updates ===");
            
            var statusUpdates = new List<string>();
            IProgress<string> progress = new Progress<string>(status =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                var message = $"[{timestamp}] {status}";
                Console.WriteLine(message);
                statusUpdates.Add(status);
            });

            var result = await Task.Run(() => FormDiscoveryService.CompareFormsWithDatabase(progress));
            
            Console.WriteLine($"\n=== Results ===");
            Console.WriteLine($"Total status updates: {statusUpdates.Count}");
            Console.WriteLine($"Has mismatch: {result.HasMismatch}");
            Console.WriteLine($"Missing forms: {result.MissingForms?.Count ?? 0}");
            Console.WriteLine($"Obsolete forms: {result.ObsoleteForms?.Count ?? 0}");
            Console.WriteLine($"Existing forms: {result.ExistingForms?.Count ?? 0}");
            Console.WriteLine($"Errors: {result.Errors?.Count ?? 0}");
            
            if (result.Errors?.Count > 0)
            {
                Console.WriteLine("Errors:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }

            var history = StatusProgressService.GetRecentHistory(20);
            Console.WriteLine($"\nStatus history entries: {history.Count}");
            
            return result;
        }

        #endregion
    }
}
