using System;
using System.Diagnostics;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for monitoring and measuring sync operation performance
    /// Provides comprehensive performance metrics collection
    /// </summary>
    public static class PerformanceMonitoringService
    {
        /// <summary>
        /// Measure sync performance with comprehensive metrics
        /// </summary>
        /// <param name="syncOperation">Sync operation to measure</param>
        /// <returns>Performance metrics</returns>
        public static SyncPerformanceMetrics MeasureSyncPerformance(Func<FormSyncResult> syncOperation)
        {
            var stopwatch = Stopwatch.StartNew();
            var startMemory = GC.GetTotalMemory(false);
            
            try
            {
                var result = syncOperation();
                stopwatch.Stop();
                
                var endMemory = GC.GetTotalMemory(false);
                
                var metrics = new SyncPerformanceMetrics
                {
                    TotalSyncTime = stopwatch.Elapsed,
                    FormsAdded = result.MissingForms?.Count ?? 0,
                    FormsRemoved = result.ObsoleteForms?.Count ?? 0,
                    MemoryUsed = endMemory - startMemory,
                    CacheHit = FormScanCacheService.GetCache().IsValid
                };
                
                SyncLoggingService.LogPerformanceMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                SyncLoggingService.LogSyncError("Performance measurement failed", ex);
                
                return new SyncPerformanceMetrics
                {
                    TotalSyncTime = stopwatch.Elapsed,
                    MemoryUsed = GC.GetTotalMemory(false) - startMemory
                };
            }
        }
        
        /// <summary>
        /// Measure async sync performance
        /// </summary>
        /// <param name="syncOperation">Async sync operation to measure</param>
        /// <returns>Performance metrics</returns>
        public static async System.Threading.Tasks.Task<SyncPerformanceMetrics> MeasureAsyncSyncPerformance(
            Func<System.Threading.Tasks.Task<FormSyncResult>> syncOperation)
        {
            var stopwatch = Stopwatch.StartNew();
            var startMemory = GC.GetTotalMemory(false);
            
            try
            {
                var result = await syncOperation();
                stopwatch.Stop();
                
                var endMemory = GC.GetTotalMemory(false);
                
                var metrics = new SyncPerformanceMetrics
                {
                    TotalSyncTime = stopwatch.Elapsed,
                    FormsAdded = result.MissingForms?.Count ?? 0,
                    FormsRemoved = result.ObsoleteForms?.Count ?? 0,
                    MemoryUsed = endMemory - startMemory,
                    CacheHit = FormScanCacheService.GetCache().IsValid
                };
                
                SyncLoggingService.LogPerformanceMetrics(metrics);
                return metrics;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                SyncLoggingService.LogSyncError("Async performance measurement failed", ex);
                
                return new SyncPerformanceMetrics
                {
                    TotalSyncTime = stopwatch.Elapsed,
                    MemoryUsed = GC.GetTotalMemory(false) - startMemory
                };
            }
        }
        
        /// <summary>
        /// Measure cache operation performance
        /// </summary>
        /// <param name="cacheOperation">Cache operation to measure</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Operation duration</returns>
        public static TimeSpan MeasureCacheOperation(Action cacheOperation, string operationName)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                cacheOperation();
                stopwatch.Stop();
                
                SyncLoggingService.LogCacheOperation($"{operationName} completed", true, 
                    $"Duration: {stopwatch.ElapsedMilliseconds}ms");
                
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                SyncLoggingService.LogSyncError($"Cache operation {operationName} failed", ex);
                return stopwatch.Elapsed;
            }
        }
        
        /// <summary>
        /// Measure database operation performance
        /// </summary>
        /// <param name="databaseOperation">Database operation to measure</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Operation metrics</returns>
        public static DatabaseOperationMetrics MeasureDatabaseOperation(
            Func<int> databaseOperation, string operationName)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var recordsAffected = databaseOperation();
                stopwatch.Stop();
                
                var metrics = new DatabaseOperationMetrics
                {
                    OperationName = operationName,
                    Duration = stopwatch.Elapsed,
                    RecordsAffected = recordsAffected,
                    Success = true
                };
                
                SyncLoggingService.LogDatabaseOperation(operationName, recordsAffected, stopwatch.Elapsed);
                return metrics;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                SyncLoggingService.LogSyncError($"Database operation {operationName} failed", ex);
                
                return new DatabaseOperationMetrics
                {
                    OperationName = operationName,
                    Duration = stopwatch.Elapsed,
                    RecordsAffected = 0,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
        
        /// <summary>
        /// Get current memory usage statistics
        /// </summary>
        /// <returns>Memory usage information</returns>
        public static MemoryUsageInfo GetMemoryUsage()
        {
            return new MemoryUsageInfo
            {
                TotalMemory = GC.GetTotalMemory(false),
                Gen0Collections = GC.CollectionCount(0),
                Gen1Collections = GC.CollectionCount(1),
                Gen2Collections = GC.CollectionCount(2),
                Timestamp = DateTime.Now
            };
        }
        
        /// <summary>
        /// Database operation metrics
        /// </summary>
        public class DatabaseOperationMetrics
        {
            public string OperationName { get; set; }
            public TimeSpan Duration { get; set; }
            public int RecordsAffected { get; set; }
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
        }
        
        /// <summary>
        /// Memory usage information
        /// </summary>
        public class MemoryUsageInfo
        {
            public long TotalMemory { get; set; }
            public int Gen0Collections { get; set; }
            public int Gen1Collections { get; set; }
            public int Gen2Collections { get; set; }
            public DateTime Timestamp { get; set; }
        }
    }
}
