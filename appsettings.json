{"SyncConfiguration": {"TransactionTimeoutMinutes": 5, "CacheExpirationMinutes": 30, "CacheDirectory": "%APPDATA%/ProManage", "FallbackCacheDirectory": "%LOCALAPPDATA%/ProManage", "EnablePersistentCache": true, "EnableProgressReporting": true, "EnableCrossMachineLocking": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 1}, "DatabaseConfiguration": {"UseAdvisoryLocks": true, "CommandTimeoutMinutes": 2, "EnableTransactionLogging": true, "IsolationLevel": "Serializable"}, "HealthCheckConfiguration": {"ApiKey": "${PROMANAGE_HEALTH_API_KEY}", "AllowedHosts": ["127.0.0.1", "localhost", "::1"], "EnableHealthEndpoint": true}, "LoggingConfiguration": {"RetentionDays": 30, "SecurityLogRetentionDays": 90, "MaxFileSizeMB": 100, "LogLevel": "Information", "SecurityLogLevel": "Warning"}}