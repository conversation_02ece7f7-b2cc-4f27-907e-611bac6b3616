using System;
using System.Linq;
using System.Threading.Tasks;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for operational monitoring and health checks
    /// Provides secure health status endpoints with API key authentication
    /// </summary>
    public static class HealthCheckService
    {
        private static readonly string API_KEY = Environment.GetEnvironmentVariable("PROMANAGE_HEALTH_API_KEY") ?? "default-dev-key";
        private static readonly string[] ALLOWED_HOSTS = { "127.0.0.1", "localhost", "::1" };
        
        /// <summary>
        /// Get sync health status with security validation
        /// </summary>
        /// <param name="apiKey">API key for authentication</param>
        /// <param name="clientHost">Client host for localhost validation</param>
        /// <returns>Health check result</returns>
        public static async Task<HealthCheckResult> GetSyncHealthStatus(string apiKey = null, string clientHost = null)
        {
            // **CRITICAL**: Validate localhost-only access
            if (!IsLocalhostRequest(clientHost))
            {
                return new HealthCheckResult
                {
                    Status = "Forbidden",
                    Message = "Health check only available from localhost",
                    Timestamp = DateTime.Now
                };
            }
            
            // **CRITICAL**: Validate API key for security
            if (!ValidateApiKey(apiKey))
            {
                return new HealthCheckResult
                {
                    Status = "Unauthorized",
                    Message = "Invalid API key",
                    Timestamp = DateTime.Now
                };
            }
            
            try
            {
                var healthData = await CollectHealthMetrics();
                return CreateHealthResponse(healthData);
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Health check failed", ex);
                return new HealthCheckResult
                {
                    Status = "Error",
                    Message = "Internal health check error",
                    Timestamp = DateTime.Now
                };
            }
        }
        
        /// <summary>
        /// Validate if request is from localhost
        /// </summary>
        /// <param name="clientHost">Client host to validate</param>
        /// <returns>True if localhost request</returns>
        private static bool IsLocalhostRequest(string clientHost)
        {
            if (string.IsNullOrEmpty(clientHost))
                return false;
                
            return ALLOWED_HOSTS.Any(host => 
                string.Equals(clientHost, host, StringComparison.OrdinalIgnoreCase));
        }
        
        /// <summary>
        /// Validate API key for security
        /// </summary>
        /// <param name="providedKey">Provided API key</param>
        /// <returns>True if valid</returns>
        private static bool ValidateApiKey(string providedKey)
        {
            // **CRITICAL**: Simple API key validation for localhost security
            return !string.IsNullOrEmpty(providedKey) && 
                   string.Equals(providedKey, API_KEY, StringComparison.Ordinal);
        }
        
        /// <summary>
        /// Collect health metrics from various services
        /// </summary>
        /// <returns>Health metrics data</returns>
        private static async Task<HealthMetrics> CollectHealthMetrics()
        {
            var cache = FormScanCacheService.GetCache();
            var lastSyncTime = cache.LastScanTime;
            var cacheValid = cache.IsValid;
            
            // Check if sync is currently in progress
            var syncInProgress = GlobalSyncMutexService.IsSyncInProgress();
            
            return new HealthMetrics
            {
                LastSyncTime = lastSyncTime,
                CacheValid = cacheValid,
                SyncInProgress = syncInProgress,
                CachedFormCount = cache.CachedFormList?.Count ?? 0
            };
        }
        
        /// <summary>
        /// Create health response from metrics
        /// </summary>
        /// <param name="metrics">Health metrics</param>
        /// <returns>Health check result</returns>
        private static HealthCheckResult CreateHealthResponse(HealthMetrics metrics)
        {
            var status = DetermineHealthStatus(metrics.LastSyncTime, metrics.CacheValid, metrics.SyncInProgress);
            
            return new HealthCheckResult
            {
                Status = status,
                LastSyncTime = metrics.LastSyncTime,
                CacheValid = metrics.CacheValid,
                SyncInProgress = metrics.SyncInProgress,
                CachedFormCount = metrics.CachedFormCount,
                Timestamp = DateTime.Now,
                Version = "1.0.0"
            };
        }
        
        /// <summary>
        /// Determine health status based on metrics
        /// </summary>
        /// <param name="lastSyncTime">Last sync time</param>
        /// <param name="cacheValid">Cache validity</param>
        /// <param name="syncInProgress">Sync in progress flag</param>
        /// <returns>Health status string</returns>
        private static string DetermineHealthStatus(DateTime lastSyncTime, bool cacheValid, bool syncInProgress)
        {
            if (syncInProgress)
                return "Syncing";
            
            if (!cacheValid || DateTime.Now.Subtract(lastSyncTime).TotalHours > 24)
                return "Stale";
            
            if (DateTime.Now.Subtract(lastSyncTime).TotalHours > 1)
                return "Warning";
            
            return "Healthy";
        }
        
        /// <summary>
        /// Internal health metrics structure
        /// </summary>
        private class HealthMetrics
        {
            public DateTime LastSyncTime { get; set; }
            public bool CacheValid { get; set; }
            public bool SyncInProgress { get; set; }
            public int CachedFormCount { get; set; }
        }
    }
}
