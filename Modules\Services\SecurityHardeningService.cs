using System;
using System.Threading.Tasks;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Compatibility wrapper for FormDiscoverySecurityService
    /// Maintains backward compatibility with existing code that references SecurityHardeningService
    /// </summary>
    public static class SecurityHardeningService
    {
        /// <summary>
        /// Validates security hardening for the form discovery system
        /// This is a wrapper method that delegates to FormDiscoverySecurityService
        /// </summary>
        /// <returns>Security validation result</returns>
        public static async Task<SecurityValidationResult> ValidateSecurityHardening()
        {
            return await FormDiscoverySecurityService.ValidateSecurityHardening();
        }
    }
}
