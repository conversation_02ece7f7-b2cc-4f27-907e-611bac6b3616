using System;
using ProManage.Modules.Models;

namespace ProManage.Modules.Interfaces
{
    /// <summary>
    /// Interface for audit logging capabilities in the Form Discovery System
    /// Future implementation will provide comprehensive audit trail functionality
    /// </summary>
    public interface IAuditLogger
    {
        /// <summary>
        /// Log form synchronization operation results
        /// </summary>
        /// <param name="result">Sync operation result details</param>
        void LogFormSync(FormSyncResult result);

        /// <summary>
        /// Log when a form is added to the permission system
        /// </summary>
        /// <param name="formName">Name of the form added</param>
        /// <param name="affectedUsers">Number of users affected</param>
        /// <param name="affectedRoles">Number of roles affected</param>
        void LogFormAdded(string formName, int affectedUsers, int affectedRoles);

        /// <summary>
        /// Log when a form is removed from the permission system
        /// </summary>
        /// <param name="formName">Name of the form removed</param>
        void LogFormRemoved(string formName);

        /// <summary>
        /// Log synchronization errors
        /// </summary>
        /// <param name="error">Error message</param>
        /// <param name="exception">Exception details (optional)</param>
        void LogSyncError(string error, Exception exception = null);

        /// <summary>
        /// Log cache operations (read, write, validate, etc.)
        /// </summary>
        /// <param name="operation">Cache operation type</param>
        /// <param name="success">Whether operation succeeded</param>
        void LogCacheOperation(string operation, bool success);

        /// <summary>
        /// Log performance metrics for sync operations
        /// </summary>
        /// <param name="metrics">Performance metrics data</param>
        void LogPerformanceMetrics(SyncPerformanceMetrics metrics);

        /// <summary>
        /// Log security-related events
        /// </summary>
        /// <param name="eventType">Type of security event</param>
        /// <param name="details">Event details</param>
        void LogSecurityEvent(string eventType, string details);
    }

    /// <summary>
    /// Empty implementation of IAuditLogger for current use
    /// TODO: Future implementation will be in Modules/Audit/ namespace
    /// </summary>
    public class EmptyAuditLogger : IAuditLogger
    {
        public void LogFormSync(FormSyncResult result) { }
        
        public void LogFormAdded(string formName, int affectedUsers, int affectedRoles) { }
        
        public void LogFormRemoved(string formName) { }
        
        public void LogSyncError(string error, Exception exception = null) { }
        
        public void LogCacheOperation(string operation, bool success) { }
        
        public void LogPerformanceMetrics(SyncPerformanceMetrics metrics) { }
        
        public void LogSecurityEvent(string eventType, string details) { }
    }
}
