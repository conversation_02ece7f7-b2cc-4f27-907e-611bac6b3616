using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Forms;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Unit tests for UI Enhancement functionality (Task 4 Implementation)
    /// Tests the mismatch detection, progress reporting, and thread safety
    /// </summary>
    [TestClass]
    public class UIEnhancementTests
    {
        private PermissionManagementForm _form;

        [TestInitialize]
        public void Setup()
        {
            // Note: These tests are designed to test the logic without requiring actual UI rendering
            // In a real scenario, you might need to use UI testing frameworks like White or Coded UI
            _form = null; // Initialize to null since we're not actually creating UI forms in these tests
        }

        [TestCleanup]
        public void Cleanup()
        {
            _form?.Dispose();
        }

        [TestMethod]
        public void SyncProgress_PercentComplete_HandlesZeroTotal()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 0,
                CompletedOperations = 0
            };

            // Act
            var percent = progress.PercentComplete;

            // Assert
            Assert.AreEqual(0, percent, "Should handle zero total operations gracefully");
        }

        [TestMethod]
        public void SyncProgress_PercentComplete_HandlesCompletion()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 10,
                CompletedOperations = 10
            };

            // Act
            var percent = progress.PercentComplete;

            // Assert
            Assert.AreEqual(100, percent, "Should handle 100% completion correctly");
        }

        [TestMethod]
        public void SyncProgress_PercentComplete_HandlesOverCompletion()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 10,
                CompletedOperations = 15
            };

            // Act
            var percent = progress.PercentComplete;

            // Assert
            Assert.AreEqual(150, percent, "Should handle over-completion (though UI should cap at 100)");
        }

        [TestMethod]
        public void SyncProgress_EstimatedTimeRemaining_WithNoProgress_ReturnsZero()
        {
            // Arrange
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 0,
                StartTime = DateTime.Now
            };

            // Act
            var timeRemaining = progress.EstimatedTimeRemaining;

            // Assert
            Assert.AreEqual(TimeSpan.Zero, timeRemaining, "Should return zero when no progress made");
        }

        [TestMethod]
        public void SyncProgress_EstimatedTimeRemaining_WithProgress_CalculatesCorrectly()
        {
            // Arrange
            var startTime = DateTime.Now.AddSeconds(-30); // Started 30 seconds ago
            var progress = new SyncProgress
            {
                TotalOperations = 100,
                CompletedOperations = 50, // 50% complete
                StartTime = startTime
            };

            // Act
            var timeRemaining = progress.EstimatedTimeRemaining;

            // Assert
            Assert.IsTrue(timeRemaining > TimeSpan.Zero, "Should calculate positive time remaining");
            Assert.IsTrue(timeRemaining < TimeSpan.FromMinutes(2), "Should be reasonable estimate");
        }

        [TestMethod]
        public void FormMismatchDetails_HasMismatches_CalculatesCorrectly()
        {
            // Arrange
            var mismatchDetails = new FormMismatchDetails
            {
                FormsOnlyInFileSystem = new List<string> { "NewForm1", "NewForm2" },
                FormsOnlyInDatabase = new List<string> { "OldForm1" },
                FormsInBoth = new List<string> { "ExistingForm1", "ExistingForm2" }
            };

            // Act & Assert
            Assert.AreEqual(3, mismatchDetails.TotalMismatches, "Should count mismatches correctly");
            Assert.IsTrue(mismatchDetails.HasMismatches, "Should detect mismatches");
        }

        [TestMethod]
        public void FormMismatchDetails_NoMismatches_CalculatesCorrectly()
        {
            // Arrange
            var mismatchDetails = new FormMismatchDetails
            {
                FormsOnlyInFileSystem = new List<string>(),
                FormsOnlyInDatabase = new List<string>(),
                FormsInBoth = new List<string> { "ExistingForm1", "ExistingForm2" }
            };

            // Act & Assert
            Assert.AreEqual(0, mismatchDetails.TotalMismatches, "Should handle no mismatches");
            Assert.IsFalse(mismatchDetails.HasMismatches, "Should detect no mismatches");
        }

        [TestMethod]
        public void FormSyncResult_SyncDuration_IsRecorded()
        {
            // Arrange
            var result = new FormSyncResult
            {
                SyncTimestamp = DateTime.Now.AddMinutes(-1)
            };

            // Act
            result.SyncDuration = DateTime.Now - result.SyncTimestamp;

            // Assert
            Assert.IsTrue(result.SyncDuration > TimeSpan.Zero, "Sync duration should be positive");
            Assert.IsTrue(result.SyncDuration < TimeSpan.FromMinutes(2), "Sync duration should be reasonable");
        }

        [TestMethod]
        public void ProgressReporter_ThreadSafety_HandlesMultipleReports()
        {
            // Arrange
            var progressReports = new List<SyncProgress>();
            var lockObject = new object();
            IProgress<SyncProgress> progress = new Progress<SyncProgress>(p =>
            {
                lock (lockObject)
                {
                    progressReports.Add(p);
                }
            });

            // Act - Simulate multiple threads reporting progress
            var tasks = new List<Task>();
            for (int i = 0; i < 10; i++)
            {
                int taskId = i;
                tasks.Add(Task.Run(() =>
                {
                    var syncProgress = new SyncProgress
                    {
                        CurrentOperation = $"Operation {taskId}",
                        CompletedOperations = taskId,
                        TotalOperations = 10,
                        StartTime = DateTime.Now
                    };
                    progress.Report(syncProgress);
                }));
            }

            Task.WaitAll(tasks.ToArray());

            // Assert
            Assert.AreEqual(10, progressReports.Count, "All progress reports should be received");
            Assert.IsTrue(progressReports.All(p => p.CurrentOperation.StartsWith("Operation")), 
                "All operations should be recorded correctly");
        }

        [TestMethod]
        public void CancellationToken_Cancellation_IsHandledGracefully()
        {
            // Arrange
            var cancellationTokenSource = new CancellationTokenSource();
            var token = cancellationTokenSource.Token;

            // Act
            cancellationTokenSource.Cancel();

            // Assert
            Assert.IsTrue(token.IsCancellationRequested, "Cancellation should be requested");
            
            // Test that OperationCanceledException is thrown when token is checked
            Assert.ThrowsException<OperationCanceledException>(() => 
                token.ThrowIfCancellationRequested());
        }

        [TestMethod]
        public async Task AsyncOperation_WithCancellation_CompletesGracefully()
        {
            // Arrange
            var cancellationTokenSource = new CancellationTokenSource();
            var token = cancellationTokenSource.Token;

            // Act
            var task = Task.Run(async () =>
            {
                await Task.Delay(100, token); // Short delay
                token.ThrowIfCancellationRequested();
                return "Completed";
            }, token);

            cancellationTokenSource.Cancel();

            // Assert
            await Assert.ThrowsExceptionAsync<OperationCanceledException>(() => task);
        }

        [TestMethod]
        public void SyncLogging_ErrorLogging_DoesNotThrow()
        {
            // Arrange
            var exception = new Exception("Test exception");

            // Act & Assert
            try
            {
                SyncLoggingService.LogSyncError("Test operation", exception);
                // If we reach here, no exception was thrown
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }
        }

        [TestMethod]
        public void SyncLogging_FormDiscovery_DoesNotThrow()
        {
            // Arrange
            var formCount = 5;
            var duration = TimeSpan.FromSeconds(2);

            // Act & Assert
            try
            {
                SyncLoggingService.LogFormDiscovery("Test discovery", formCount, duration);
                // If we reach here, no exception was thrown
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }
        }

        [TestMethod]
        public void SyncLogging_SyncComplete_DoesNotThrow()
        {
            // Arrange
            var formsAdded = 3;
            var formsRemoved = 1;
            var duration = TimeSpan.FromSeconds(5);

            // Act & Assert
            try
            {
                SyncLoggingService.LogSyncComplete(formsAdded, formsRemoved, duration);
                // If we reach here, no exception was thrown
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }
        }

        [TestMethod]
        public void FormScanCache_IsValid_ChecksExpiration()
        {
            // Arrange
            var cache = new FormScanCache
            {
                LastScanTime = DateTime.Now.AddMinutes(-25), // Within 30-minute window
                FormListHash = "test-hash",
                CachedFormList = new List<string> { "TestForm" }
            };

            // Act
            var isValid = cache.IsValid;

            // Assert
            Assert.IsTrue(isValid, "Cache should be valid within 30-minute window");

            // Test expired cache
            cache.LastScanTime = DateTime.Now.AddMinutes(-35); // Outside 30-minute window
            isValid = cache.IsValid;
            Assert.IsFalse(isValid, "Cache should be invalid after 30 minutes");
        }

        [TestMethod]
        public void GlobalSyncMutex_GenerateAdvisoryLockKey_IsConsistent()
        {
            // Act
            var key1 = GlobalSyncMutexService.GenerateAdvisoryLockKey();
            var key2 = GlobalSyncMutexService.GenerateAdvisoryLockKey();

            // Assert
            Assert.AreEqual(key1, key2, "Advisory lock key should be consistent");
            Assert.AreNotEqual(0, key1, "Advisory lock key should not be zero");
        }
    }
}
