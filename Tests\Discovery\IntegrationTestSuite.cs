using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Integration tests for Task 6: Testing Strategy and Scenarios
    /// End-to-end workflow testing for Form Discovery Service
    /// </summary>
    [TestClass]
    public class IntegrationTestSuite
    {
        [TestInitialize]
        public void Setup()
        {
            // Initialize services for integration testing
            SyncLoggingService.ConfigureRollingFileLogger();
            FormScanCacheService.ClearCache();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up after each test
            FormScanCacheService.ClearCache();
        }

        #region End-to-End Sync Workflow Tests

        [TestMethod]
        public void EndToEndSync_NewFormDetection_WorkflowComplete()
        {
            Console.WriteLine("Testing end-to-end new form detection workflow...");

            try
            {
                // Step 1: Simulate form discovery
                var discoveredForms = new List<string> { "NewTestForm", "ExistingForm" };
                
                // Step 2: Update cache with discovered forms
                FormScanCacheService.UpdateCache(discoveredForms);
                
                // Step 3: Verify cache was updated
                var cache = FormScanCacheService.GetCache();
                Assert.IsNotNull(cache, "Cache should be created");
                Assert.AreEqual(2, cache.CachedFormList.Count, "Cache should contain discovered forms");
                
                // Step 4: Verify cache validity
                Assert.IsTrue(cache.IsValid, "Cache should be valid after update");
                
                // Step 5: Test skip scan logic
                var shouldSkip = FormScanCacheService.ShouldSkipScan();
                Assert.IsTrue(shouldSkip, "Should skip scan with valid cache");
                
                Console.WriteLine("✅ End-to-end new form detection workflow completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ End-to-end workflow failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public void EndToEndSync_CacheExpiration_WorkflowComplete()
        {
            Console.WriteLine("Testing end-to-end cache expiration workflow...");

            try
            {
                // Step 1: Create expired cache
                var expiredCache = new FormScanCache
                {
                    Version = 1,
                    LastScanTime = DateTime.Now.AddMinutes(-35), // Expired
                    CachedFormList = new List<string> { "ExpiredForm" },
                    HashingAlgorithm = "SHA256"
                };

                // Step 2: Verify cache is expired
                Assert.IsFalse(expiredCache.IsValid, "Cache should be expired");

                // Step 3: Test skip scan with expired cache
                FormScanCacheService.ClearCache();
                var shouldSkip = FormScanCacheService.ShouldSkipScan();
                Assert.IsFalse(shouldSkip, "Should not skip scan with expired cache");

                // Step 4: Update with fresh data
                var freshForms = new List<string> { "FreshForm1", "FreshForm2" };
                FormScanCacheService.UpdateCache(freshForms);

                // Step 5: Verify fresh cache
                var freshCache = FormScanCacheService.GetCache();
                Assert.IsTrue(freshCache.IsValid, "Fresh cache should be valid");
                Assert.AreEqual(2, freshCache.CachedFormList.Count, "Fresh cache should contain new forms");

                Console.WriteLine("✅ End-to-end cache expiration workflow completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Cache expiration workflow failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Cross-Component Integration Tests

        [TestMethod]
        public void CrossComponent_CacheAndHealthCheck_Integration()
        {
            Console.WriteLine("Testing cache and health check integration...");

            try
            {
                // Step 1: Clear cache and verify health status
                FormScanCacheService.ClearCache();
                
                // Step 2: Check health status with empty cache
                var healthResult = HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost").Result;
                Assert.IsNotNull(healthResult, "Health check should return result");
                
                // Step 3: Update cache with forms
                var testForms = new List<string> { "HealthTestForm1", "HealthTestForm2" };
                FormScanCacheService.UpdateCache(testForms);
                
                // Step 4: Check health status with populated cache
                var updatedHealthResult = HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost").Result;
                Assert.IsNotNull(updatedHealthResult, "Health check should return updated result");
                Assert.AreEqual(2, updatedHealthResult.CachedFormCount, "Health check should reflect cache count");

                Console.WriteLine("✅ Cache and health check integration completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Cross-component integration failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public void CrossComponent_ConfigurationAndServices_Integration()
        {
            Console.WriteLine("Testing configuration and services integration...");

            try
            {
                // Step 1: Load configuration
                var syncConfig = ConfigurationService.GetSyncConfiguration();
                var dbConfig = ConfigurationService.GetDatabaseConfiguration();
                
                Assert.IsNotNull(syncConfig, "Sync configuration should be loaded");
                Assert.IsNotNull(dbConfig, "Database configuration should be loaded");
                
                // Step 2: Verify configuration values are used
                Assert.IsTrue(syncConfig.EnablePersistentCache, "Persistent cache should be enabled");
                Assert.IsTrue(dbConfig.UseAdvisoryLocks, "Advisory locks should be enabled");
                
                // Step 3: Test configuration reload
                ConfigurationService.ReloadConfiguration();
                var reloadedConfig = ConfigurationService.GetSyncConfiguration();
                Assert.IsNotNull(reloadedConfig, "Configuration should reload successfully");

                Console.WriteLine("✅ Configuration and services integration completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Configuration integration failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Performance Integration Tests

        [TestMethod]
        public void PerformanceIntegration_CacheOperations_MeasuredCorrectly()
        {
            Console.WriteLine("Testing performance monitoring integration...");

            try
            {
                // Step 1: Measure cache clear operation
                var clearDuration = PerformanceMonitoringService.MeasureCacheOperation(() =>
                {
                    FormScanCacheService.ClearCache();
                }, "CacheClear");

                Assert.IsTrue(clearDuration >= TimeSpan.Zero, "Clear operation should be measured");

                // Step 2: Measure cache update operation
                var updateDuration = PerformanceMonitoringService.MeasureCacheOperation(() =>
                {
                    var forms = new List<string> { "PerfTestForm1", "PerfTestForm2", "PerfTestForm3" };
                    FormScanCacheService.UpdateCache(forms);
                }, "CacheUpdate");

                Assert.IsTrue(updateDuration >= TimeSpan.Zero, "Update operation should be measured");

                // Step 3: Verify cache was updated
                var cache = FormScanCacheService.GetCache();
                Assert.AreEqual(3, cache.CachedFormList.Count, "Cache should contain performance test forms");

                Console.WriteLine("✅ Performance monitoring integration completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Performance integration failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Error Recovery Integration Tests

        [TestMethod]
        public void ErrorRecovery_CacheCorruption_RecoveryWorkflow()
        {
            Console.WriteLine("Testing cache corruption recovery workflow...");

            try
            {
                // Step 1: Create valid cache
                var validForms = new List<string> { "ValidForm1", "ValidForm2" };
                FormScanCacheService.UpdateCache(validForms);
                
                // Step 2: Verify cache is valid
                var cache = FormScanCacheService.GetCache();
                Assert.IsTrue(cache.IsValid, "Cache should be valid initially");

                // Step 3: Simulate corruption by clearing and testing recovery
                FormScanCacheService.ClearCache();
                
                // Step 4: Verify graceful handling of missing cache
                var recoveredCache = FormScanCacheService.GetCache();
                Assert.IsNotNull(recoveredCache, "Should return new cache when missing");
                Assert.AreEqual(0, recoveredCache.CachedFormList.Count, "New cache should be empty");

                // Step 5: Verify system can continue operating
                var newForms = new List<string> { "RecoveredForm1" };
                FormScanCacheService.UpdateCache(newForms);
                
                var finalCache = FormScanCacheService.GetCache();
                Assert.AreEqual(1, finalCache.CachedFormList.Count, "System should recover and continue operating");

                Console.WriteLine("✅ Cache corruption recovery workflow completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error recovery workflow failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Security Integration Tests

        [TestMethod]
        public async Task SecurityIntegration_HealthCheckAccess_SecurityWorkflow()
        {
            Console.WriteLine("Testing health check security integration...");

            try
            {
                // Step 1: Test unauthorized access
                var unauthorizedResult = await HealthCheckService.GetSyncHealthStatus("wrong-key", "localhost");
                Assert.AreEqual("Unauthorized", unauthorizedResult.Status, "Should reject wrong API key");

                // Step 2: Test non-localhost access
                var forbiddenResult = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "remote-host");
                Assert.AreEqual("Forbidden", forbiddenResult.Status, "Should reject non-localhost access");

                // Step 3: Test valid access
                var validResult = await HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost");
                Assert.AreNotEqual("Unauthorized", validResult.Status, "Should allow valid access");
                Assert.AreNotEqual("Forbidden", validResult.Status, "Should allow localhost access");

                Console.WriteLine("✅ Health check security integration completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Security integration failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Stress Testing

        [TestMethod]
        public void StressTest_MultipleCacheOperations_SystemStability()
        {
            Console.WriteLine("Testing system stability under multiple cache operations...");

            try
            {
                // Perform multiple cache operations rapidly
                for (int i = 0; i < 10; i++)
                {
                    var forms = new List<string> { $"StressForm{i}_1", $"StressForm{i}_2" };
                    FormScanCacheService.UpdateCache(forms);
                    
                    var cache = FormScanCacheService.GetCache();
                    Assert.IsNotNull(cache, $"Cache should be valid after operation {i}");
                    
                    var shouldSkip = FormScanCacheService.ShouldSkipScan();
                    Assert.IsTrue(shouldSkip, $"Should skip scan after operation {i}");
                }

                Console.WriteLine("✅ Stress test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Stress test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        /// <summary>
        /// Run all integration tests
        /// </summary>
        public static void RunAllIntegrationTests()
        {
            Console.WriteLine("=== Integration Test Suite ===");
            
            var testSuite = new IntegrationTestSuite();
            
            try
            {
                testSuite.Setup();
                
                testSuite.EndToEndSync_NewFormDetection_WorkflowComplete();
                testSuite.EndToEndSync_CacheExpiration_WorkflowComplete();
                testSuite.CrossComponent_CacheAndHealthCheck_Integration();
                testSuite.CrossComponent_ConfigurationAndServices_Integration();
                testSuite.PerformanceIntegration_CacheOperations_MeasuredCorrectly();
                testSuite.ErrorRecovery_CacheCorruption_RecoveryWorkflow();
                testSuite.SecurityIntegration_HealthCheckAccess_SecurityWorkflow().Wait();
                testSuite.StressTest_MultipleCacheOperations_SystemStability();
                
                Console.WriteLine("✅ All integration tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Integration tests failed: {ex.Message}");
                throw;
            }
            finally
            {
                testSuite.Cleanup();
            }
        }
    }
}
