using System;
using System.IO;
using System.Text.Json;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for managing configurable timeouts and settings
    /// Loads configuration from appsettings.json with fallback to defaults
    /// </summary>
    public static class ConfigurationService
    {
        private static SyncConfiguration _config;
        
        /// <summary>
        /// Get sync configuration with lazy loading
        /// </summary>
        /// <returns>Sync configuration object</returns>
        public static SyncConfiguration GetSyncConfiguration()
        {
            if (_config == null)
            {
                _config = LoadConfigurationFromFile();
            }
            return _config;
        }
        
        /// <summary>
        /// Load configuration from appsettings.json file
        /// </summary>
        /// <returns>Configuration object</returns>
        private static SyncConfiguration LoadConfigurationFromFile()
        {
            try
            {
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    var configRoot = JsonSerializer.Deserialize<ConfigurationRoot>(json, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                    
                    if (configRoot?.SyncConfiguration != null)
                    {
                        SyncLoggingService.LogCacheOperation("Configuration loaded from file", true);
                        return configRoot.SyncConfiguration;
                    }
                }
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Configuration load failed, using defaults", ex);
            }
            
            SyncLoggingService.LogCacheOperation("Using default configuration", true);
            return GetDefaultConfiguration();
        }
        
        /// <summary>
        /// Get default configuration values
        /// </summary>
        /// <returns>Default sync configuration</returns>
        private static SyncConfiguration GetDefaultConfiguration()
        {
            return new SyncConfiguration
            {
                TransactionTimeout = TimeSpan.FromMinutes(5),
                CacheExpiration = TimeSpan.FromMinutes(30),
                CacheDirectory = "%APPDATA%/ProManage",
                FallbackCacheDirectory = "%LOCALAPPDATA%/ProManage",
                EnablePersistentCache = true,
                EnableProgressReporting = true,
                EnableCrossMachineLocking = true,
                MaxRetryAttempts = 3,
                RetryDelay = TimeSpan.FromSeconds(1)
            };
        }
        
        /// <summary>
        /// Get database configuration
        /// </summary>
        /// <returns>Database configuration object</returns>
        public static DatabaseConfiguration GetDatabaseConfiguration()
        {
            try
            {
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
                
                if (File.Exists(configPath))
                {
                    var json = File.ReadAllText(configPath);
                    var configRoot = JsonSerializer.Deserialize<ConfigurationRoot>(json, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                    
                    if (configRoot?.DatabaseConfiguration != null)
                    {
                        return configRoot.DatabaseConfiguration;
                    }
                }
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("Database configuration load failed, using defaults", ex);
            }
            
            return GetDefaultDatabaseConfiguration();
        }
        
        /// <summary>
        /// Get default database configuration
        /// </summary>
        /// <returns>Default database configuration</returns>
        private static DatabaseConfiguration GetDefaultDatabaseConfiguration()
        {
            return new DatabaseConfiguration
            {
                UseAdvisoryLocks = true,
                CommandTimeout = TimeSpan.FromMinutes(2),
                EnableTransactionLogging = true,
                IsolationLevel = "Serializable"
            };
        }
        
        /// <summary>
        /// Reload configuration from file
        /// </summary>
        public static void ReloadConfiguration()
        {
            _config = null;
            _config = LoadConfigurationFromFile();
            SyncLoggingService.LogCacheOperation("Configuration reloaded", true);
        }
        
        /// <summary>
        /// Root configuration structure for JSON deserialization
        /// </summary>
        private class ConfigurationRoot
        {
            public SyncConfiguration SyncConfiguration { get; set; }
            public DatabaseConfiguration DatabaseConfiguration { get; set; }
            public HealthCheckConfiguration HealthCheckConfiguration { get; set; }
            public LoggingConfiguration LoggingConfiguration { get; set; }
        }
        
        /// <summary>
        /// Health check configuration
        /// </summary>
        public class HealthCheckConfiguration
        {
            public string ApiKey { get; set; }
            public string[] AllowedHosts { get; set; }
            public bool EnableHealthEndpoint { get; set; }
        }
        
        /// <summary>
        /// Logging configuration
        /// </summary>
        public class LoggingConfiguration
        {
            public int RetentionDays { get; set; }
            public int SecurityLogRetentionDays { get; set; }
            public int MaxFileSizeMB { get; set; }
            public string LogLevel { get; set; }
            public string SecurityLogLevel { get; set; }
        }
    }
}
