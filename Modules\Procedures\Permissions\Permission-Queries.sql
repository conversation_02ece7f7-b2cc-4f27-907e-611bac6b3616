-- Permission-Queries.sql
-- Additional SQL queries for RBAC permission operations
-- PostgreSQL syntax for ProManage application

-- [GetEffectiveUserPermissions] --
-- Get effective permissions for a user (combines role and user-specific permissions)
-- Usage: Replace $1 with user_id parameter
SELECT
    f.form_name,
    COALESCE(up.read_permission, rp.read_permission, false) as effective_read,
    COALESCE(up.new_permission, rp.new_permission, false) as effective_new,
    COALESCE(up.edit_permission, rp.edit_permission, false) as effective_edit,
    COALESCE(up.delete_permission, rp.delete_permission, false) as effective_delete,
    COALESCE(up.print_permission, rp.print_permission, false) as effective_print,
    CASE
        WHEN up.perm_id IS NOT NULL THEN 'UserOverride'
        ELSE 'Role'
    END as permission_source
FROM (
    SELECT DISTINCT form_name FROM role_permissions
    UNION
    SELECT DISTINCT form_name FROM user_permissions
) f
LEFT JOIN users u ON u.user_id = $1
LEFT JOIN role_permissions rp ON rp.role_id = u.role_id AND rp.form_name = f.form_name
LEFT JOIN user_permissions up ON up.user_id = $1 AND up.form_name = f.form_name
ORDER BY f.form_name;
-- [End] --

-- [GetUserPermissionSummary] --
-- Get permission summary for a specific user
-- Usage: Replace $1 with user_id parameter
SELECT
    u.user_id,
    u.username,
    u.full_name,
    r.role_name,
    COUNT(DISTINCT rp.form_name) as total_forms_in_role,
    COUNT(DISTINCT up.form_name) as user_overrides,
    gp.can_create_users,
    gp.can_edit_users,
    gp.can_delete_users,
    gp.can_print_users
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
LEFT JOIN role_permissions rp ON r.role_id = rp.role_id
LEFT JOIN user_permissions up ON u.user_id = up.user_id
LEFT JOIN global_permissions gp ON u.user_id = gp.user_id
WHERE u.user_id = $1
GROUP BY u.user_id, u.username, u.full_name, r.role_name,
         gp.can_create_users, gp.can_edit_users, gp.can_delete_users, gp.can_print_users;
-- [End] --

-- [GetRolePermissionMatrix] --
-- Get permission matrix for all roles and forms
SELECT 
    r.role_name,
    rp.form_name,
    rp.read_permission,
    rp.new_permission,
    rp.edit_permission,
    rp.delete_permission,
    rp.print_permission
FROM roles r
LEFT JOIN role_permissions rp ON r.role_id = rp.role_id
WHERE r.is_active = true
ORDER BY r.role_name, rp.form_name;
-- [End] --

-- [GetUsersWithPermissionOverrides] --
-- Get all users who have permission overrides
SELECT
    u.user_id,
    u.username,
    u.full_name,
    r.role_name,
    COUNT(up.perm_id) as override_count
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
INNER JOIN user_permissions up ON u.user_id = up.user_id
GROUP BY u.user_id, u.username, u.full_name, r.role_name
ORDER BY override_count DESC, u.username;
-- [End] --

-- [GetFormPermissionSummary] --
-- Get permission summary for a specific form across all roles
-- Usage: Replace $1 with form_name parameter
SELECT
    $1 as form_name,
    r.role_name,
    rp.read_permission,
    rp.new_permission,
    rp.edit_permission,
    rp.delete_permission,
    rp.print_permission,
    COUNT(u.user_id) as users_in_role
FROM roles r
LEFT JOIN role_permissions rp ON r.role_id = rp.role_id AND rp.form_name = $1
LEFT JOIN users u ON u.role_id = r.role_id
WHERE r.is_active = true
GROUP BY r.role_id, r.role_name, rp.read_permission, rp.new_permission,
         rp.edit_permission, rp.delete_permission, rp.print_permission
ORDER BY r.role_name;
-- [End] --



-- [ValidateUserPermissions] --
-- Validate that user permissions are consistent
SELECT 
    u.user_id,
    u.username,
    'Missing role assignment' as issue
FROM users u
WHERE u.role_id IS NULL
UNION ALL
SELECT 
    u.user_id,
    u.username,
    'Role does not exist' as issue
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.role_id IS NOT NULL AND r.role_id IS NULL
UNION ALL
SELECT 
    u.user_id,
    u.username,
    'Assigned to inactive role' as issue
FROM users u
INNER JOIN roles r ON u.role_id = r.role_id
WHERE r.is_active = false;
-- [End] --

-- [GetSystemPermissionStatistics] --
-- Get overall system permission statistics
SELECT 
    (SELECT COUNT(*) FROM roles WHERE is_active = true) as active_roles,
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(DISTINCT form_name) FROM role_permissions) as forms_in_system,
    (SELECT COUNT(*) FROM user_permissions) as user_overrides,
    (SELECT COUNT(*) FROM global_permissions) as users_with_global_perms,
    (SELECT COUNT(*) FROM users WHERE role_id IS NULL) as users_without_role;
-- [End] --



-- [GetFormsNotInPermissionSystem] --
-- Identify forms that might be missing from permission system
-- This would need to be enhanced with actual form discovery logic
SELECT 
    'Form discovery requires application-level logic' as message,
    'Use FormsConfigurationService to manage form definitions' as recommendation;
-- [End] --
