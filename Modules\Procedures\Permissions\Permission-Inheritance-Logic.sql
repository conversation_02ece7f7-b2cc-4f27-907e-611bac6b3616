-- Permission-Inheritance-Logic.sql
-- SQL procedures for implementing permission inheritance logic
-- PostgreSQL syntax for ProManage application

-- [UpdateRolePermissionsWithInheritance] --
-- Update role permissions and automatically propagate to users
-- Usage: Call with role_id and permission updates
CREATE OR REPLACE FUNCTION update_role_permissions_with_inheritance(
    p_role_id INTEGER,
    p_form_name VARCHAR(100),
    p_read_permission BOOLEAN,
    p_new_permission BOOLEAN,
    p_edit_permission BOOLEAN,
    p_delete_permission BOOLEAN,
    p_print_permission BOOLEAN
) RETURNS BOOLEAN AS $$
DECLARE
    v_affected_users INTEGER;
BEGIN
    -- Update or insert role permission
    INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission, created_date)
    VALUES (p_role_id, p_form_name, p_read_permission, p_new_permission, p_edit_permission, p_delete_permission, p_print_permission, CURRENT_TIMESTAMP)
    ON CONFLICT (role_id, form_name) 
    DO UPDATE SET 
        read_permission = p_read_permission,
        new_permission = p_new_permission,
        edit_permission = p_edit_permission,
        delete_permission = p_delete_permission,
        print_permission = p_print_permission,
        created_date = CURRENT_TIMESTAMP;

    -- Auto-update users who don't have explicit overrides for this form
    -- This implements the inheritance logic
    UPDATE users 
    SET role_id = role_id -- Trigger update to refresh permissions cache
    WHERE role_id = p_role_id
    AND user_id NOT IN (
        SELECT user_id 
        FROM user_permissions 
        WHERE form_name = p_form_name
    );

    GET DIAGNOSTICS v_affected_users = ROW_COUNT;
    
    -- Note: Inheritance update completed for affected users

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
-- [End] --

-- [GetEffectiveUserPermissionsWithInheritance] --
-- Get effective permissions for a user with proper inheritance logic
-- Usage: SELECT * FROM get_effective_user_permissions_with_inheritance(user_id)
CREATE OR REPLACE FUNCTION get_effective_user_permissions_with_inheritance(p_user_id INTEGER)
RETURNS TABLE (
    form_name VARCHAR(100),
    role_read_permission BOOLEAN,
    role_new_permission BOOLEAN,
    role_edit_permission BOOLEAN,
    role_delete_permission BOOLEAN,
    role_print_permission BOOLEAN,
    user_read_permission BOOLEAN,
    user_new_permission BOOLEAN,
    user_edit_permission BOOLEAN,
    user_delete_permission BOOLEAN,
    user_print_permission BOOLEAN,
    effective_read BOOLEAN,
    effective_new BOOLEAN,
    effective_edit BOOLEAN,
    effective_delete BOOLEAN,
    effective_print BOOLEAN,
    permission_source VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.form_name,
        COALESCE(rp.read_permission, false) as role_read_permission,
        COALESCE(rp.new_permission, false) as role_new_permission,
        COALESCE(rp.edit_permission, false) as role_edit_permission,
        COALESCE(rp.delete_permission, false) as role_delete_permission,
        COALESCE(rp.print_permission, false) as role_print_permission,
        COALESCE(up.read_permission, COALESCE(rp.read_permission, false)) as user_read_permission,
        COALESCE(up.new_permission, COALESCE(rp.new_permission, false)) as user_new_permission,
        COALESCE(up.edit_permission, COALESCE(rp.edit_permission, false)) as user_edit_permission,
        COALESCE(up.delete_permission, COALESCE(rp.delete_permission, false)) as user_delete_permission,
        COALESCE(up.print_permission, COALESCE(rp.print_permission, false)) as user_print_permission,
        -- Effective permissions: User override takes precedence, otherwise use role
        COALESCE(up.read_permission, rp.read_permission, false) as effective_read,
        COALESCE(up.new_permission, rp.new_permission, false) as effective_new,
        COALESCE(up.edit_permission, rp.edit_permission, false) as effective_edit,
        COALESCE(up.delete_permission, rp.delete_permission, false) as effective_delete,
        COALESCE(up.print_permission, rp.print_permission, false) as effective_print,
        CASE 
            WHEN up.perm_id IS NOT NULL THEN 'USER_OVERRIDE'
            ELSE 'ROLE_INHERITED'
        END as permission_source
    FROM (
        SELECT DISTINCT form_name FROM role_permissions
        UNION
        SELECT DISTINCT form_name FROM user_permissions
    ) f
    LEFT JOIN users u ON u.user_id = p_user_id
    LEFT JOIN role_permissions rp ON rp.role_id = u.role_id AND rp.form_name = f.form_name
    LEFT JOIN user_permissions up ON up.user_id = p_user_id AND up.form_name = f.form_name
    ORDER BY f.form_name;
END;
$$ LANGUAGE plpgsql;
-- [End] --

-- [UpdateUserPermissionWithOverride] --
-- Update user permission with override logic
-- Usage: Call to set user-specific permission that overrides role
CREATE OR REPLACE FUNCTION update_user_permission_with_override(
    p_user_id INTEGER,
    p_form_name VARCHAR(100),
    p_read_permission BOOLEAN,
    p_new_permission BOOLEAN,
    p_edit_permission BOOLEAN,
    p_delete_permission BOOLEAN,
    p_print_permission BOOLEAN
) RETURNS BOOLEAN AS $$
BEGIN
    -- Insert or update user permission override
    INSERT INTO user_permissions (user_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission, created_date)
    VALUES (p_user_id, p_form_name, p_read_permission, p_new_permission, p_edit_permission, p_delete_permission, p_print_permission, CURRENT_TIMESTAMP)
    ON CONFLICT (user_id, form_name) 
    DO UPDATE SET 
        read_permission = p_read_permission,
        new_permission = p_new_permission,
        edit_permission = p_edit_permission,
        delete_permission = p_delete_permission,
        print_permission = p_print_permission,
        created_date = CURRENT_TIMESTAMP;

    -- Note: User permission override applied

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
-- [End] --

-- [RemoveUserPermissionOverride] --
-- Remove user permission override to revert to role permissions
-- Usage: Call to remove user override and inherit from role
CREATE OR REPLACE FUNCTION remove_user_permission_override(
    p_user_id INTEGER,
    p_form_name VARCHAR(100)
) RETURNS BOOLEAN AS $$
BEGIN
    -- Delete user permission override
    DELETE FROM user_permissions 
    WHERE user_id = p_user_id AND form_name = p_form_name;

    -- Note: User permission override removed

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
-- [End] --


