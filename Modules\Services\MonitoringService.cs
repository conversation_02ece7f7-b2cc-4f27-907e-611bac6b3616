using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for comprehensive system monitoring and alerting
    /// Provides operational visibility and health monitoring
    /// </summary>
    public static class MonitoringService
    {
        private static readonly List<string> _alerts = new List<string>();
        
        /// <summary>
        /// Perform comprehensive system health monitoring
        /// </summary>
        /// <returns>System health status</returns>
        public static async Task<SystemHealthStatus> GetSystemHealthStatus()
        {
            var status = new SystemHealthStatus
            {
                CheckTime = DateTime.Now,
                CheckId = Guid.NewGuid().ToString()
            };

            try
            {
                // Check cache health
                status.Components.Add(CheckCacheHealth());

                // Check database health
                status.Components.Add(await CheckDatabaseHealth());

                // Check security health
                status.Components.Add(await CheckSecurityHealth());

                // Check performance health
                status.Components.Add(CheckPerformanceHealth());

                // Check disk space
                status.Components.Add(CheckDiskSpaceHealth());

                // Check memory usage
                status.Components.Add(CheckMemoryHealth());

                // Calculate overall health
                status.OverallHealth = DetermineOverallHealth(status.Components);
                status.HealthyComponents = status.Components.FindAll(c => c.Status == "Healthy").Count;
                status.TotalComponents = status.Components.Count;

                // Generate alerts if needed
                GenerateAlerts(status);

                SyncLoggingService.LogInfo($"System health check completed: {status.OverallHealth}");
                return status;
            }
            catch (Exception ex)
            {
                SyncLoggingService.LogSyncError("System health check failed", ex);
                status.OverallHealth = "Critical";
                status.ErrorMessage = ex.Message;
                return status;
            }
        }

        /// <summary>
        /// Check cache system health
        /// </summary>
        private static HealthComponent CheckCacheHealth()
        {
            var component = new HealthComponent
            {
                Name = "Cache System",
                Category = "Performance"
            };

            try
            {
                var cache = FormScanCacheService.GetCache();
                
                if (cache == null)
                {
                    component.Status = "Critical";
                    component.Details = "Cache system not accessible";
                    component.Recommendation = "Check cache directory permissions";
                    return component;
                }

                if (!cache.IsValid)
                {
                    component.Status = "Warning";
                    component.Details = "Cache expired or invalid";
                    component.Recommendation = "Cache will be refreshed on next sync";
                    return component;
                }

                var cacheAge = DateTime.Now - cache.LastScanTime;
                if (cacheAge.TotalHours > 24)
                {
                    component.Status = "Warning";
                    component.Details = $"Cache is {cacheAge.TotalHours:F1} hours old";
                    component.Recommendation = "Consider running sync to refresh cache";
                    return component;
                }

                component.Status = "Healthy";
                component.Details = $"Cache valid with {cache.CachedFormList?.Count ?? 0} forms";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Critical";
                component.Details = $"Cache health check failed: {ex.Message}";
                component.Recommendation = "Review cache configuration";
                return component;
            }
        }

        /// <summary>
        /// Check database health
        /// </summary>
        private static async Task<HealthComponent> CheckDatabaseHealth()
        {
            var component = new HealthComponent
            {
                Name = "Database System",
                Category = "Infrastructure"
            };

            try
            {
                // Test database connectivity
                var lockKey = GlobalSyncMutexService.GenerateAdvisoryLockKey();
                if (lockKey <= 0)
                {
                    component.Status = "Critical";
                    component.Details = "Database advisory lock generation failed";
                    component.Recommendation = "Check database connectivity";
                    return component;
                }

                // Test sync status
                var syncInProgress = GlobalSyncMutexService.IsSyncInProgress();
                
                component.Status = "Healthy";
                component.Details = $"Database accessible, sync in progress: {syncInProgress}";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Critical";
                component.Details = $"Database health check failed: {ex.Message}";
                component.Recommendation = "Check database connection and configuration";
                return component;
            }
        }

        /// <summary>
        /// Check security health
        /// </summary>
        private static async Task<HealthComponent> CheckSecurityHealth()
        {
            var component = new HealthComponent
            {
                Name = "Security System",
                Category = "Security"
            };

            try
            {
                var securityValidation = await SecurityHardeningService.ValidateSecurityHardening();
                
                if (!securityValidation.Passed)
                {
                    component.Status = "Critical";
                    component.Details = $"Security validation failed: {securityValidation.PassedCount}/{securityValidation.TotalCount} checks passed";
                    component.Recommendation = "Review security validation results and address failures";
                    return component;
                }

                component.Status = "Healthy";
                component.Details = $"All security checks passed ({securityValidation.TotalCount}/{securityValidation.TotalCount})";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Critical";
                component.Details = $"Security health check failed: {ex.Message}";
                component.Recommendation = "Review security configuration";
                return component;
            }
        }

        /// <summary>
        /// Check performance health
        /// </summary>
        private static HealthComponent CheckPerformanceHealth()
        {
            var component = new HealthComponent
            {
                Name = "Performance System",
                Category = "Performance"
            };

            try
            {
                var memoryInfo = PerformanceMonitoringService.GetMemoryUsage();
                var memoryMB = memoryInfo.TotalMemory / 1024 / 1024;

                if (memoryMB > 1000) // 1GB threshold
                {
                    component.Status = "Warning";
                    component.Details = $"High memory usage: {memoryMB:F0}MB";
                    component.Recommendation = "Monitor memory usage and consider optimization";
                    return component;
                }

                if (memoryMB > 2000) // 2GB critical threshold
                {
                    component.Status = "Critical";
                    component.Details = $"Critical memory usage: {memoryMB:F0}MB";
                    component.Recommendation = "Immediate memory optimization required";
                    return component;
                }

                component.Status = "Healthy";
                component.Details = $"Memory usage: {memoryMB:F0}MB";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Warning";
                component.Details = $"Performance check failed: {ex.Message}";
                component.Recommendation = "Review performance monitoring";
                return component;
            }
        }

        /// <summary>
        /// Check disk space health
        /// </summary>
        private static HealthComponent CheckDiskSpaceHealth()
        {
            var component = new HealthComponent
            {
                Name = "Disk Space",
                Category = "Infrastructure"
            };

            try
            {
                var cachePath = FormScanCacheService.GetCacheFilePath();
                var driveInfo = new DriveInfo(Path.GetPathRoot(cachePath));
                
                var freeSpaceGB = driveInfo.AvailableFreeSpace / 1024 / 1024 / 1024;
                var totalSpaceGB = driveInfo.TotalSize / 1024 / 1024 / 1024;
                var usedPercentage = ((double)(driveInfo.TotalSize - driveInfo.AvailableFreeSpace) / driveInfo.TotalSize) * 100;

                if (usedPercentage > 90)
                {
                    component.Status = "Critical";
                    component.Details = $"Disk space critical: {usedPercentage:F1}% used ({freeSpaceGB:F1}GB free)";
                    component.Recommendation = "Free up disk space immediately";
                    return component;
                }

                if (usedPercentage > 80)
                {
                    component.Status = "Warning";
                    component.Details = $"Disk space low: {usedPercentage:F1}% used ({freeSpaceGB:F1}GB free)";
                    component.Recommendation = "Monitor disk space and plan cleanup";
                    return component;
                }

                component.Status = "Healthy";
                component.Details = $"Disk space: {usedPercentage:F1}% used ({freeSpaceGB:F1}GB free)";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Warning";
                component.Details = $"Disk space check failed: {ex.Message}";
                component.Recommendation = "Review disk space monitoring";
                return component;
            }
        }

        /// <summary>
        /// Check memory health
        /// </summary>
        private static HealthComponent CheckMemoryHealth()
        {
            var component = new HealthComponent
            {
                Name = "Memory Usage",
                Category = "Performance"
            };

            try
            {
                var process = Process.GetCurrentProcess();
                var workingSetMB = process.WorkingSet64 / 1024 / 1024;
                var privateMemoryMB = process.PrivateMemorySize64 / 1024 / 1024;

                if (workingSetMB > 500)
                {
                    component.Status = "Warning";
                    component.Details = $"High working set: {workingSetMB:F0}MB (Private: {privateMemoryMB:F0}MB)";
                    component.Recommendation = "Monitor memory usage patterns";
                    return component;
                }

                component.Status = "Healthy";
                component.Details = $"Working set: {workingSetMB:F0}MB (Private: {privateMemoryMB:F0}MB)";
                return component;
            }
            catch (Exception ex)
            {
                component.Status = "Warning";
                component.Details = $"Memory check failed: {ex.Message}";
                component.Recommendation = "Review memory monitoring";
                return component;
            }
        }

        /// <summary>
        /// Determine overall system health
        /// </summary>
        private static string DetermineOverallHealth(List<HealthComponent> components)
        {
            var criticalCount = components.FindAll(c => c.Status == "Critical").Count;
            var warningCount = components.FindAll(c => c.Status == "Warning").Count;

            if (criticalCount > 0)
                return "Critical";
            
            if (warningCount > 0)
                return "Warning";
            
            return "Healthy";
        }

        /// <summary>
        /// Generate alerts based on health status
        /// </summary>
        private static void GenerateAlerts(SystemHealthStatus status)
        {
            foreach (var component in status.Components)
            {
                if (component.Status == "Critical")
                {
                    var alert = $"CRITICAL: {component.Name} - {component.Details}";
                    _alerts.Add(alert);
                    SyncLoggingService.LogSecurityEvent("CriticalAlert", alert);
                }
                else if (component.Status == "Warning")
                {
                    var alert = $"WARNING: {component.Name} - {component.Details}";
                    _alerts.Add(alert);
                    SyncLoggingService.LogSecurityEvent("WarningAlert", alert);
                }
            }
        }

        /// <summary>
        /// Get current alerts
        /// </summary>
        /// <returns>List of active alerts</returns>
        public static List<string> GetActiveAlerts()
        {
            return new List<string>(_alerts);
        }

        /// <summary>
        /// Clear all alerts
        /// </summary>
        public static void ClearAlerts()
        {
            _alerts.Clear();
            SyncLoggingService.LogInfo("All alerts cleared");
        }
    }
}
