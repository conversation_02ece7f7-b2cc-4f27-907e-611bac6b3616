# 🚀 Discovery System – Permission Sync Logic (Final Instruction)

## 🟢 Trigger

* The **discovery system runs when the `PermissionManagementForm` is opened**.
* It scans the form names and compares them with database entries.
* If any mismatch (extra/missing form names) is found, a label is shown beside the `Refresh` button on both tabs:

  > 🔴 *“Data mismatch – Click Refresh to update.”*

---

## 📁 Step 1 – Scan Forms from Folder

1. The system scans the **`MainForm` folder**.
2. It collects all files with the `.cs` extension.
3. For each file, it extracts the **base form name** by removing the `.cs` extension.

   * Example: `RoleMasterForm.cs` → `RoleMasterForm`
4. These form names are used to **compare against `form_name` fields in the DB**.
5. ⚠️ **Important**: This process is **read-only** for files. No files are modified or renamed.

---

## ❌ Pre-Check Logic (On Form Open)

1. When the **`PermissionManagementForm`** opens, the discovery system runs immediately.
2. It checks for form name mismatches between:

   * The scanned folder list
   * The `user_permissions` and `role_permissions` tables
3. If mismatch is found:

   * Show notification label on **both tabs**:

     > 🔴 *“Data mismatch – Click Refresh to update.”*

---

## ♻️ Refresh Button Behavior

* The **Refresh button on either tab** executes the **same sync logic**.
* Clicking Refresh will:

  * Add any missing form entries
  * Delete any orphaned form entries
  * Update **both** `user_permissions` and `role_permissions` tables
* After sync, show a label:

  > ✅ *“Data is up to date.”*

---

## ✅ ADD SCENARIO – Insert Missing Form Permissions

### Steps

1. Get all form names (without `.cs`) from folder.
2. Fetch distinct form names from `user_permissions` and `role_permissions`.
3. Compare the lists.
4. If a form is missing in DB:

   * Insert into `user_permissions` (one row per `user_id`)
   * Insert into `role_permissions` (one row per `role_id`)

### Default Values

* Permissions (`read`, `new`, `edit`, `delete`, `print`) = `false`
* `user_permissions`:

  * `override_reason = NULL`
  * `created_date = NOW()`
  * `created_by = NULL` ✅ *(Updated from 'system\_discovery')*
* `role_permissions`:

  * `created_date = NOW()`
  * `updated_date = NULL`

### Example Scenario

* **Users**: user1, user2
* **Roles**: role1, role2, role3, role4
* **Folder**: `FormA.cs`, `FormB.cs`
* **DB**: only has `FormA`

**Result:**

* Add `FormB` to both tables:

  * user1, user2 → FormB
  * role1—role4 → FormB

### Sample SQL:

```sql
-- Insert into user_permissions
INSERT INTO user_permissions (
    user_id, form_name, read_permission, new_permission, edit_permission,
    delete_permission, print_permission, override_reason,
    created_date, created_by
)
SELECT u.user_id, 'FormB', false, false, false, false, false,
       NULL, NOW(), NULL
FROM users u
WHERE NOT EXISTS (
    SELECT 1 FROM user_permissions up
    WHERE up.user_id = u.user_id AND up.form_name = 'FormB'
);

-- Insert into role_permissions
INSERT INTO role_permissions (
    role_id, form_name, read_permission, new_permission, edit_permission,
    delete_permission, print_permission, created_date, updated_date
)
SELECT r.role_id, 'FormB', false, false, false, false, false,
       NOW(), NULL
FROM roles r
WHERE NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.role_id AND rp.form_name = 'FormB'
);
```

---

## ❌ DELETE SCENARIO – Remove Obsolete Form Permissions

### Steps

1. Get form names from folder.
2. Get form names from both permission tables.
3. If any form is in DB but missing in folder:

   * Delete that form from both `user_permissions` and `role_permissions`

### Example

* Folder: `FormA.cs`
* DB: has `FormA`, `FormC`
* **Result**: Delete all entries of `FormC` from both tables

### Sample SQL:

```sql
DELETE FROM user_permissions WHERE form_name = 'FormC';
DELETE FROM role_permissions WHERE form_name = 'FormC';
```

---

## ✅ Summary Table

| Action        | Condition                                   | Operation                                   |
| ------------- | ------------------------------------------- | ------------------------------------------- |
| Add Form      | Found in folder, missing in DB              | Insert into both permission tables          |
| Ignore Form   | Exists in both DB and folder                | Do nothing                                  |
| Delete Form   | Exists in DB but missing in folder          | Delete from both permission tables          |
| Trigger Check | When `PermissionManagementForm` opens       | Display label if mismatch found             |
| Run Sync      | When Refresh button is clicked (either tab) | Update both tables and clear mismatch label |
