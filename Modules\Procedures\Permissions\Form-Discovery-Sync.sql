-- =============================================
-- Form Discovery Sync Procedures
-- Purpose: Database synchronization logic for Form Discovery Service
-- Author: ProManage Form Discovery System
-- Created: Task 3 Implementation
-- =============================================

-- 1. Get all form names from both permission tables
CREATE OR REPLACE FUNCTION sp_GetAllFormNamesFromPermissions()
RETURNS TABLE(form_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT UPPER(up.form_name) as form_name
    FROM user_permissions up
    UNION
    SELECT DISTINCT UPPER(rp.form_name) as form_name
    FROM role_permissions rp
    ORDER BY form_name;
END;
$$ LANGUAGE plpgsql;

-- 2. Add form to all users with default permissions (false)
CREATE OR REPLACE FUNCTION sp_AddFormToAllUsers(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    user_rec RECORD;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Add form to all existing users
    FOR user_rec IN SELECT user_id FROM users WHERE active = true LOOP
        INSERT INTO user_permissions (
            user_id, form_name, read_permission, new_permission, 
            edit_permission, delete_permission, print_permission,
            created_date
        ) VALUES (
            user_rec.user_id, p_form_name, false, false, 
            false, false, false,
            NOW()
        )
        ON CONFLICT (user_id, UPPER(form_name)) DO NOTHING;
        
        GET DIAGNOSTICS affected_rows = affected_rows + ROW_COUNT;
    END LOOP;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 3. Add form to all roles with default permissions (false)
CREATE OR REPLACE FUNCTION sp_AddFormToAllRoles(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    role_rec RECORD;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Add form to all existing roles
    FOR role_rec IN SELECT role_id FROM roles WHERE active = true LOOP
        INSERT INTO role_permissions (
            role_id, form_name, read_permission, new_permission,
            edit_permission, delete_permission, print_permission,
            created_date
        ) VALUES (
            role_rec.role_id, p_form_name, false, false,
            false, false, false,
            NOW()
        )
        ON CONFLICT (role_id, UPPER(form_name)) DO NOTHING;
        
        GET DIAGNOSTICS affected_rows = affected_rows + ROW_COUNT;
    END LOOP;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 4. Remove form from both permission tables
CREATE OR REPLACE FUNCTION sp_RemoveFormFromPermissions(p_form_name TEXT)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Normalize form name to UpperInvariant
    p_form_name := UPPER(p_form_name);
    
    -- Remove from user_permissions
    DELETE FROM user_permissions WHERE UPPER(form_name) = p_form_name;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    affected_rows := affected_rows + temp_count;
    
    -- Remove from role_permissions
    DELETE FROM role_permissions WHERE UPPER(form_name) = p_form_name;
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    affected_rows := affected_rows + temp_count;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- 5. Complete sync with SERIALIZABLE isolation + hash-based advisory lock
CREATE OR REPLACE FUNCTION sp_BatchSyncForms(
    p_forms_to_add TEXT[],
    p_forms_to_remove TEXT[]
)
RETURNS JSON AS $$
DECLARE
    lock_key BIGINT;
    result JSON;
    forms_added INTEGER := 0;
    forms_removed INTEGER := 0;
    users_affected INTEGER := 0;
    roles_affected INTEGER := 0;
    form_name TEXT;
    temp_count INTEGER;
BEGIN
    -- Generate consistent hash-based lock key to avoid collisions
    lock_key := ('x' || substr(md5('ProManage_FormSync'), 1, 16))::bit(64)::bigint;

    -- Try to acquire advisory lock (cross-machine safety)
    IF NOT pg_try_advisory_lock(lock_key) THEN
        result := json_build_object(
            'success', false,
            'error', 'Sync already in progress on another client',
            'forms_added', 0,
            'forms_removed', 0
        );
        RETURN result;
    END IF;

    -- Set isolation level for concurrent safety
    SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

    BEGIN
        -- Add missing forms
        IF p_forms_to_add IS NOT NULL THEN
            FOREACH form_name IN ARRAY p_forms_to_add LOOP
                -- Add to users
                SELECT sp_AddFormToAllUsers(form_name) INTO temp_count;
                users_affected := users_affected + temp_count;
                
                -- Add to roles
                SELECT sp_AddFormToAllRoles(form_name) INTO temp_count;
                roles_affected := roles_affected + temp_count;
                
                forms_added := forms_added + 1;
            END LOOP;
        END IF;

        -- Remove obsolete forms
        IF p_forms_to_remove IS NOT NULL THEN
            FOREACH form_name IN ARRAY p_forms_to_remove LOOP
                SELECT sp_RemoveFormFromPermissions(form_name) INTO temp_count;
                forms_removed := forms_removed + 1;
            END LOOP;
        END IF;

        -- Build success result
        result := json_build_object(
            'success', true,
            'forms_added', forms_added,
            'forms_removed', forms_removed,
            'users_affected', users_affected,
            'roles_affected', roles_affected,
            'timestamp', NOW()
        );

        -- Release advisory lock
        PERFORM pg_advisory_unlock(lock_key);
        RETURN result;
        
    EXCEPTION
        WHEN OTHERS THEN
            -- Release advisory lock on error
            PERFORM pg_advisory_unlock(lock_key);
            
            result := json_build_object(
                'success', false,
                'error', SQLERRM,
                'forms_added', 0,
                'forms_removed', 0
            );
            RETURN result;
    END;
END;
$$ LANGUAGE plpgsql;

-- 6. Create optimized indexes for performance
CREATE OR REPLACE FUNCTION sp_CreateOptimizedIndexes()
RETURNS VOID AS $$
BEGIN
    -- B-tree indexes on form_name columns for performance
    CREATE INDEX IF NOT EXISTS idx_user_permissions_form_name
        ON user_permissions(UPPER(form_name));
    CREATE INDEX IF NOT EXISTS idx_role_permissions_form_name
        ON role_permissions(UPPER(form_name));
    
    -- Composite indexes for user/role + form lookups
    CREATE UNIQUE INDEX IF NOT EXISTS idx_user_permissions_unique
        ON user_permissions(user_id, UPPER(form_name));
    CREATE UNIQUE INDEX IF NOT EXISTS idx_role_permissions_unique
        ON role_permissions(role_id, UPPER(form_name));
    
    -- Performance indexes for large datasets (1000+ users/roles)
    CREATE INDEX IF NOT EXISTS idx_user_permissions_composite
        ON user_permissions(user_id, UPPER(form_name), read_permission);
    CREATE INDEX IF NOT EXISTS idx_role_permissions_composite
        ON role_permissions(role_id, UPPER(form_name), read_permission);
END;
$$ LANGUAGE plpgsql;

-- 7. One-time migration for existing data (run before adding constraints)
CREATE OR REPLACE FUNCTION sp_MigrateFormNameCasing()
RETURNS VOID AS $$
BEGIN
    -- Update existing data to UpperInvariant before adding constraints
    UPDATE user_permissions SET form_name = UPPER(form_name) WHERE form_name != UPPER(form_name);
    UPDATE role_permissions SET form_name = UPPER(form_name) WHERE form_name != UPPER(form_name);

    -- Remove duplicates that may exist after case normalization
    DELETE FROM user_permissions a USING user_permissions b
    WHERE a.perm_id > b.perm_id AND a.user_id = b.user_id AND UPPER(a.form_name) = UPPER(b.form_name);

    DELETE FROM role_permissions a USING role_permissions b
    WHERE a.permission_id > b.permission_id AND a.role_id = b.role_id AND UPPER(a.form_name) = UPPER(b.form_name);
END;
$$ LANGUAGE plpgsql;

-- 8. Validate form name casing to prevent duplicates
CREATE OR REPLACE FUNCTION sp_ValidateFormNameCasing()
RETURNS TABLE(table_name TEXT, duplicate_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 'user_permissions'::TEXT, COUNT(*)
    FROM (
        SELECT user_id, UPPER(form_name) as normalized_name, COUNT(*) as cnt
        FROM user_permissions
        GROUP BY user_id, UPPER(form_name)
        HAVING COUNT(*) > 1
    ) duplicates
    UNION ALL
    SELECT 'role_permissions'::TEXT, COUNT(*)
    FROM (
        SELECT role_id, UPPER(form_name) as normalized_name, COUNT(*) as cnt
        FROM role_permissions
        GROUP BY role_id, UPPER(form_name)
        HAVING COUNT(*) > 1
    ) duplicates;
END;
$$ LANGUAGE plpgsql;

-- 9. Scheduled cleanup for large deletes
CREATE OR REPLACE FUNCTION sp_MaintenanceVacuum()
RETURNS VOID AS $$
BEGIN
    -- Schedule VACUUM ANALYZE if >10% churn detected
    VACUUM ANALYZE user_permissions;
    VACUUM ANALYZE role_permissions;
END;
$$ LANGUAGE plpgsql;

-- 10. Index bloat management
CREATE OR REPLACE FUNCTION sp_MaintenanceReindex()
RETURNS VOID AS $$
BEGIN
    -- Reindex if indexes are >500MB to prevent bloat
    IF pg_total_relation_size('idx_user_permissions_unique') > 500 * 1024 * 1024 THEN
        REINDEX INDEX idx_user_permissions_unique;
    END IF;

    IF pg_total_relation_size('idx_role_permissions_unique') > 500 * 1024 * 1024 THEN
        REINDEX INDEX idx_role_permissions_unique;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- DEPLOYMENT SEQUENCE (CRITICAL)
-- =============================================
-- 1. Run sp_MigrateFormNameCasing() - Clean existing data
-- 2. Run sp_CreateOptimizedIndexes() - Create performance indexes
-- 3. Deploy all other procedures - Core sync functionality
-- 4. Schedule maintenance - Set up automated maintenance

-- =============================================
-- USAGE EXAMPLES
-- =============================================
-- Get all forms from database:
-- SELECT * FROM sp_GetAllFormNamesFromPermissions();

-- Add a new form to all users and roles:
-- SELECT sp_AddFormToAllUsers('NEWFORM');
-- SELECT sp_AddFormToAllRoles('NEWFORM');

-- Remove a form from all permissions:
-- SELECT sp_RemoveFormFromPermissions('OLDFORM');

-- Batch sync with advisory lock:
-- SELECT sp_BatchSyncForms(ARRAY['FORM1', 'FORM2'], ARRAY['OLDFORM1']);

-- Validate data integrity:
-- SELECT * FROM sp_ValidateFormNameCasing();

-- Create performance indexes:
-- SELECT sp_CreateOptimizedIndexes();

-- Maintenance operations:
-- SELECT sp_MaintenanceVacuum();
-- SELECT sp_MaintenanceReindex();
