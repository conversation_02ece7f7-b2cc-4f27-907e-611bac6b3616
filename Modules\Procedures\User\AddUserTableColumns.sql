-- AddUserTableColumns.sql
-- <PERSON><PERSON><PERSON> to add new columns to the users table for enhanced user management

-- Add new columns to users table
ALTER TABLE users
ADD COLUMN department VARCHAR(50),
ADD COLUMN phone VARCHAR(20),
ADD COLUMN designation VARCHAR(50),
ADD COLUMN short_name VA<PERSON>HAR(50),
ADD COLUMN photo_path TEXT;

-- Add global permission columns to users table
ALTER TABLE users
ADD COLUMN can_read BOOLEAN DEFAULT TRUE,
ADD COLUMN can_create BOOLEAN DEFAULT TRUE,
ADD COLUMN can_edit BOOLEAN DEFAULT TRUE,
ADD COLUMN can_delete BOOLEAN DEFAULT TRUE,
ADD COLUMN can_print BOOLEAN DEFAULT TRUE;

-- Add comments for documentation
COMMENT ON COLUMN users.department IS 'User department or division';
COMMENT ON COLUMN users.phone IS 'User phone number';
COMMENT ON COLUMN users.designation IS 'User job title or designation';
COMMENT ON COLUMN users.short_name IS 'User short display name';
COMMENT ON COLUMN users.photo_path IS 'Path to user photo file or base64 encoded image';

-- Add comments for global permission columns
COMMENT ON COLUMN users.can_read IS 'Global permission to read/view forms';
COMMENT ON COLUMN users.can_create IS 'Global permission to create new records';
COMMENT ON COLUMN users.can_edit IS 'Global permission to edit existing records';
COMMENT ON COLUMN users.can_delete IS 'Global permission to delete records';
COMMENT ON COLUMN users.can_print IS 'Global permission to print reports';

-- Verify the changes
SELECT column_name, data_type, character_maximum_length, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'users' 
ORDER BY ordinal_position;
