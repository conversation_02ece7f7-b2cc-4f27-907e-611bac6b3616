using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Performance tests for Task 6: Testing Strategy and Scenarios
    /// Large dataset and concurrent operation testing
    /// </summary>
    [TestClass]
    public class PerformanceTestSuite
    {
        private const int LARGE_DATASET_SIZE = 1000;
        private const int PERFORMANCE_THRESHOLD_MS = 5000; // 5 seconds max for large operations

        [TestInitialize]
        public void Setup()
        {
            SyncLoggingService.ConfigureRollingFileLogger();
            FormScanCacheService.ClearCache();
        }

        [TestCleanup]
        public void Cleanup()
        {
            FormScanCacheService.ClearCache();
        }

        #region Large Dataset Performance Tests

        [TestMethod]
        public void Performance_LargeFormList_CacheOperations()
        {
            Console.WriteLine($"Testing cache performance with {LARGE_DATASET_SIZE} forms...");

            try
            {
                // Generate large form list
                var largeForms = GenerateLargeFormList(LARGE_DATASET_SIZE);
                
                // Test cache update performance
                var stopwatch = Stopwatch.StartNew();
                FormScanCacheService.UpdateCache(largeForms);
                stopwatch.Stop();
                
                Console.WriteLine($"Cache update took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < PERFORMANCE_THRESHOLD_MS, 
                    $"Cache update should complete within {PERFORMANCE_THRESHOLD_MS}ms");

                // Test cache retrieval performance
                stopwatch.Restart();
                var cache = FormScanCacheService.GetCache();
                stopwatch.Stop();
                
                Console.WriteLine($"Cache retrieval took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < 1000, "Cache retrieval should be fast");
                Assert.AreEqual(LARGE_DATASET_SIZE, cache.CachedFormList.Count, "All forms should be cached");

                // Test hash generation performance
                stopwatch.Restart();
                var hash = FormScanCacheService.GenerateFormListHash(largeForms);
                stopwatch.Stop();
                
                Console.WriteLine($"Hash generation took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000, "Hash generation should be reasonably fast");
                Assert.IsFalse(string.IsNullOrEmpty(hash), "Hash should be generated");

                Console.WriteLine("✅ Large dataset cache performance test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Large dataset performance test failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public void Performance_FormNameValidation_LargeDataset()
        {
            Console.WriteLine($"Testing form name validation performance with {LARGE_DATASET_SIZE} forms...");

            try
            {
                var largeForms = GenerateLargeFormList(LARGE_DATASET_SIZE);
                
                var stopwatch = Stopwatch.StartNew();
                var validationResults = new List<bool>();
                
                foreach (var form in largeForms)
                {
                    validationResults.Add(PermissionSyncService.ValidateFormName(form));
                }
                
                stopwatch.Stop();
                
                Console.WriteLine($"Validation of {LARGE_DATASET_SIZE} forms took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < PERFORMANCE_THRESHOLD_MS, 
                    "Form validation should complete within threshold");
                Assert.AreEqual(LARGE_DATASET_SIZE, validationResults.Count(r => r), 
                    "All generated forms should be valid");

                Console.WriteLine("✅ Form name validation performance test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Form validation performance test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Concurrent Operation Tests

        [TestMethod]
        public void Performance_ConcurrentCacheAccess_ThreadSafety()
        {
            Console.WriteLine("Testing concurrent cache access performance...");

            try
            {
                var tasks = new List<Task>();
                var results = new List<bool>();
                var lockObject = new object();

                // Create multiple concurrent tasks
                for (int i = 0; i < 10; i++)
                {
                    int taskId = i;
                    tasks.Add(Task.Run(() =>
                    {
                        try
                        {
                            var forms = new List<string> { $"ConcurrentForm{taskId}_1", $"ConcurrentForm{taskId}_2" };
                            FormScanCacheService.UpdateCache(forms);
                            
                            var cache = FormScanCacheService.GetCache();
                            var shouldSkip = FormScanCacheService.ShouldSkipScan();
                            
                            lock (lockObject)
                            {
                                results.Add(cache != null && shouldSkip);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Concurrent task {taskId} failed: {ex.Message}");
                            lock (lockObject)
                            {
                                results.Add(false);
                            }
                        }
                    }));
                }

                // Wait for all tasks to complete
                var stopwatch = Stopwatch.StartNew();
                Task.WaitAll(tasks.ToArray(), TimeSpan.FromSeconds(30));
                stopwatch.Stop();

                Console.WriteLine($"Concurrent operations took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < 10000, "Concurrent operations should complete reasonably fast");
                Assert.AreEqual(10, results.Count, "All concurrent tasks should complete");
                Assert.IsTrue(results.All(r => r), "All concurrent operations should succeed");

                Console.WriteLine("✅ Concurrent cache access test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Concurrent access test failed: {ex.Message}");
                throw;
            }
        }

        [TestMethod]
        public async Task Performance_ConcurrentHealthChecks_ResponseTime()
        {
            Console.WriteLine("Testing concurrent health check performance...");

            try
            {
                var tasks = new List<Task<HealthCheckResult>>();

                // Create multiple concurrent health check requests
                for (int i = 0; i < 20; i++)
                {
                    tasks.Add(HealthCheckService.GetSyncHealthStatus("default-dev-key", "localhost"));
                }

                var stopwatch = Stopwatch.StartNew();
                var results = await Task.WhenAll(tasks);
                stopwatch.Stop();

                Console.WriteLine($"20 concurrent health checks took: {stopwatch.ElapsedMilliseconds}ms");
                Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, "Concurrent health checks should be fast");
                Assert.AreEqual(20, results.Length, "All health checks should complete");
                Assert.IsTrue(results.All(r => !string.IsNullOrEmpty(r.Status)), "All health checks should return status");

                Console.WriteLine("✅ Concurrent health check test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Concurrent health check test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Memory Performance Tests

        [TestMethod]
        public void Performance_MemoryUsage_LargeOperations()
        {
            Console.WriteLine("Testing memory usage during large operations...");

            try
            {
                var initialMemory = PerformanceMonitoringService.GetMemoryUsage();
                Console.WriteLine($"Initial memory: {initialMemory.TotalMemory / 1024 / 1024:F1} MB");

                // Perform large operation
                var largeForms = GenerateLargeFormList(LARGE_DATASET_SIZE);
                FormScanCacheService.UpdateCache(largeForms);

                var afterOperationMemory = PerformanceMonitoringService.GetMemoryUsage();
                Console.WriteLine($"Memory after operation: {afterOperationMemory.TotalMemory / 1024 / 1024:F1} MB");

                var memoryIncrease = afterOperationMemory.TotalMemory - initialMemory.TotalMemory;
                Console.WriteLine($"Memory increase: {memoryIncrease / 1024 / 1024:F1} MB");

                // Memory increase should be reasonable (less than 100MB for 1000 forms)
                Assert.IsTrue(memoryIncrease < 100 * 1024 * 1024, "Memory usage should be reasonable");

                // Force garbage collection and check memory cleanup
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var afterGCMemory = PerformanceMonitoringService.GetMemoryUsage();
                Console.WriteLine($"Memory after GC: {afterGCMemory.TotalMemory / 1024 / 1024:F1} MB");

                Console.WriteLine("✅ Memory usage test completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Memory usage test failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Cache Performance Comparison Tests

        [TestMethod]
        public void Performance_CacheHitVsMiss_Comparison()
        {
            Console.WriteLine("Testing cache hit vs miss performance...");

            try
            {
                var testForms = GenerateLargeFormList(100);

                // Test cache miss (first access)
                FormScanCacheService.ClearCache();
                var stopwatch = Stopwatch.StartNew();
                FormScanCacheService.UpdateCache(testForms);
                var cache1 = FormScanCacheService.GetCache();
                stopwatch.Stop();
                var cacheMissTime = stopwatch.ElapsedMilliseconds;

                Console.WriteLine($"Cache miss time: {cacheMissTime}ms");

                // Test cache hit (subsequent access)
                stopwatch.Restart();
                var cache2 = FormScanCacheService.GetCache();
                var shouldSkip = FormScanCacheService.ShouldSkipScan();
                stopwatch.Stop();
                var cacheHitTime = stopwatch.ElapsedMilliseconds;

                Console.WriteLine($"Cache hit time: {cacheHitTime}ms");

                // Cache hit should be significantly faster
                Assert.IsTrue(cacheHitTime < cacheMissTime, "Cache hit should be faster than cache miss");
                Assert.IsTrue(shouldSkip, "Should skip scan with valid cache");
                Assert.AreEqual(cache1.CachedFormList.Count, cache2.CachedFormList.Count, "Cache should be consistent");

                Console.WriteLine($"Performance improvement: {(double)cacheMissTime / cacheHitTime:F1}x faster");
                Console.WriteLine("✅ Cache performance comparison completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Cache performance comparison failed: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private List<string> GenerateLargeFormList(int count)
        {
            var forms = new List<string>();
            for (int i = 0; i < count; i++)
            {
                forms.Add($"TestForm_{i:D4}");
            }
            return forms;
        }

        #endregion

        /// <summary>
        /// Run all performance tests
        /// </summary>
        public static void RunAllPerformanceTests()
        {
            Console.WriteLine("=== Performance Test Suite ===");
            
            var testSuite = new PerformanceTestSuite();
            
            try
            {
                testSuite.Setup();
                
                testSuite.Performance_LargeFormList_CacheOperations();
                testSuite.Performance_FormNameValidation_LargeDataset();
                testSuite.Performance_ConcurrentCacheAccess_ThreadSafety();
                testSuite.Performance_ConcurrentHealthChecks_ResponseTime().Wait();
                testSuite.Performance_MemoryUsage_LargeOperations();
                testSuite.Performance_CacheHitVsMiss_Comparison();
                
                Console.WriteLine("✅ All performance tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Performance tests failed: {ex.Message}");
                throw;
            }
            finally
            {
                testSuite.Cleanup();
            }
        }
    }
}
