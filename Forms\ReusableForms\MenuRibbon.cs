using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraBars;
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Models.PermissionManagementForm;
// Removed static using directive - not needed since we're using the full type names

namespace ProManage.Forms.ReusableForms
{
    /// <summary>
    /// Centralized MenuRibbon User Control with permission-based functionality
    /// Provides consistent ribbon interface across all forms with context-aware button management
    /// </summary>
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(MenuRibbon))]
    [Description("Centralized MenuRibbon User Control with permission-based functionality")]
    [Category("ProManage Controls")]
    [DesignTimeVisible(true)]
    public partial class MenuRibbon : UserControl
    {
        #region Private Fields

        private string _formName = "";
        private int _currentUserId = 0;
        private FormPermissionSet _currentPermissions;
        private bool _isEditMode = false;
        private bool _hasUnsavedChanges = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the form name for permission checking
        /// </summary>
        public string FormName
        {
            get => _formName;
            set
            {
                _formName = value;
                RefreshPermissions();
            }
        }

        /// <summary>
        /// Gets or sets the current user ID for permission checking
        /// </summary>
        public int CurrentUserId
        {
            get => _currentUserId;
            set
            {
                _currentUserId = value;
                RefreshPermissions();
            }
        }

        /// <summary>
        /// Gets or sets whether the form is in edit mode
        /// </summary>
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Gets or sets whether there are unsaved changes
        /// </summary>
        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set
            {
                _hasUnsavedChanges = value;
                UpdateButtonStates();
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event fired when New button is clicked
        /// </summary>
        public event EventHandler NewClicked;

        /// <summary>
        /// Event fired when Edit button is clicked
        /// </summary>
        public event EventHandler EditClicked;

        /// <summary>
        /// Event fired when Save button is clicked
        /// </summary>
        public event EventHandler SaveClicked;

        /// <summary>
        /// Event fired when Cancel button is clicked
        /// </summary>
        public event EventHandler CancelClicked;

        /// <summary>
        /// Event fired when Delete button is clicked
        /// </summary>
        public event EventHandler DeleteClicked;

        /// <summary>
        /// Event fired when Print button is clicked
        /// </summary>
        public event EventHandler PrintClicked;

        /// <summary>
        /// Event fired when Print Preview button is clicked
        /// </summary>
        public event EventHandler PrintPreviewClicked;

        /// <summary>
        /// Event fired when First button is clicked
        /// </summary>
        public event EventHandler FirstClicked;

        /// <summary>
        /// Event fired when Previous button is clicked
        /// </summary>
        public event EventHandler PreviousClicked;

        /// <summary>
        /// Event fired when Next button is clicked
        /// </summary>
        public event EventHandler NextClicked;

        /// <summary>
        /// Event fired when Last button is clicked
        /// </summary>
        public event EventHandler LastClicked;

        /// <summary>
        /// Event fired when Add Row button is clicked
        /// </summary>
        public event EventHandler AddRowClicked;

        #endregion

        #region Constructor

        public MenuRibbon()
        {
            InitializeComponent();

            // Only initialize ribbon in runtime, not in designer
            if (!DesignMode)
            {
                InitializeRibbon();

                // Subscribe to global permission changes
                ProManage.Modules.Services.GlobalPermissionService.GlobalPermissionsChanged += OnGlobalPermissionsChanged;
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize ribbon with event handlers and default settings
        /// </summary>
        private void InitializeRibbon()
        {
            // Skip initialization in design mode
            if (DesignMode) return;
            
            try
            {
                // Wire up button events
                BarButtonItemNew.ItemClick += (s, e) => NewClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemEdit.ItemClick += (s, e) => EditClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemSave.ItemClick += (s, e) => SaveClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemCancel.ItemClick += (s, e) => CancelClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemDelete.ItemClick += (s, e) => DeleteClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemPrint.ItemClick += (s, e) => PrintClicked?.Invoke(this, EventArgs.Empty);
                BarButtonPrintPreview.ItemClick += (s, e) => PrintPreviewClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemFirst.ItemClick += (s, e) => FirstClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemPrevious.ItemClick += (s, e) => PreviousClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemNext.ItemClick += (s, e) => NextClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemLast.ItemClick += (s, e) => LastClicked?.Invoke(this, EventArgs.Empty);
                BarButtonItemAddRow.ItemClick += (s, e) => AddRowClicked?.Invoke(this, EventArgs.Empty);

                // Set initial button states
                UpdateButtonStates();
            }
            catch (Exception)
            {
                // Ignore errors during initialization in runtime
            }
        }

        #endregion

        #region Permission Management

        /// <summary>
        /// Refresh permissions for the current form and user
        /// </summary>
        public void RefreshPermissions()
        {
            // Skip in design mode
            if (DesignMode) return;
            
            try
            {
                if (!string.IsNullOrEmpty(_formName) && _currentUserId > 0)
                {
                    // Get effective permissions for the current user and form
                    _currentPermissions = PermissionService.GetUserEffectivePermissions(_currentUserId, _formName);
                    UpdateButtonStates();
                }
            }
            catch (Exception)
            {
                // Default to basic permissions on error
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Update button enabled/disabled states based on global and form-specific permissions
        /// </summary>
        private void UpdateButtonStates()
        {
            try
            {
                // Get effective permissions (global + form-specific)
                bool canRead = HasEffectivePermission("read");
                bool canCreate = HasEffectivePermission("new");
                bool canEdit = HasEffectivePermission("edit");
                bool canDelete = HasEffectivePermission("delete");
                bool canPrint = HasEffectivePermission("print");

                // Basic operation buttons
                BarButtonItemNew.Enabled = canCreate && !_isEditMode;
                BarButtonItemEdit.Enabled = canEdit && !_isEditMode;
                BarButtonItemSave.Enabled = _isEditMode && _hasUnsavedChanges;
                BarButtonItemCancel.Enabled = _isEditMode;
                BarButtonItemDelete.Enabled = canDelete && !_isEditMode;

                // Print buttons
                BarButtonItemPrint.Enabled = canPrint;
                BarButtonPrintPreview.Enabled = canPrint;

                // Navigation buttons (enabled if can read)
                BarButtonItemFirst.Enabled = canRead && !_isEditMode;
                BarButtonItemPrevious.Enabled = canRead && !_isEditMode;
                BarButtonItemNext.Enabled = canRead && !_isEditMode;
                BarButtonItemLast.Enabled = canRead && !_isEditMode;

                // Grid operations
                BarButtonItemAddRow.Enabled = canEdit && _isEditMode;
                tglStatus.Enabled = canEdit;
            }
            catch (Exception)
            {
                // On error, disable all buttons for safety
                DisableAllButtons();
            }
        }

        /// <summary>
        /// Disable all buttons for safety
        /// </summary>
        private void DisableAllButtons()
        {
            BarButtonItemNew.Enabled = false;
            BarButtonItemEdit.Enabled = false;
            BarButtonItemSave.Enabled = false;
            BarButtonItemCancel.Enabled = false;
            BarButtonItemDelete.Enabled = false;
            BarButtonItemPrint.Enabled = false;
            BarButtonPrintPreview.Enabled = false;
            BarButtonItemFirst.Enabled = false;
            BarButtonItemPrevious.Enabled = false;
            BarButtonItemNext.Enabled = false;
            BarButtonItemLast.Enabled = false;
            BarButtonItemAddRow.Enabled = false;
            tglStatus.Enabled = false;
        }

        /// <summary>
        /// Check if user has effective permission (global AND form-specific)
        /// </summary>
        /// <param name="permissionType">Type of permission to check</param>
        /// <returns>True if user has both global and form-specific permission</returns>
        private bool HasEffectivePermission(string permissionType)
        {
            try
            {
                // Skip permission checking in design mode
                if (DesignMode) return true;

                if (_currentUserId <= 0) return false;

                // First check global permission (first-level filter)
                bool hasGlobal = ProManage.Modules.Services.GlobalPermissionService.HasGlobalPermission(_currentUserId, permissionType);
                if (!hasGlobal) return false;

                // Then check form-specific permission (second-level filter)
                if (_currentPermissions == null) return true; // If no form restrictions, allow

                string lowerType = permissionType.ToLower();
                switch (lowerType)
                {
                    case "read":
                        return _currentPermissions.CanRead;
                    case "create":
                    case "new":
                        return _currentPermissions.CanCreate;
                    case "edit":
                        return _currentPermissions.CanEdit;
                    case "delete":
                        return _currentPermissions.CanDelete;
                    case "print":
                        return _currentPermissions.CanPrint;
                    default:
                        return false;
                }
            }
            catch (Exception)
            {
                return false; // Default to deny on error
            }
        }

        /// <summary>
        /// Handle global permission changes from other forms
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Event arguments</param>
        private void OnGlobalPermissionsChanged(object sender, EventArgs e)
        {
            try
            {
                // Refresh button states with new global permissions for current user
                UpdateButtonStates();
            }
            catch (Exception)
            {
                // Ignore errors in event handling
            }
        }

        #endregion

        #region Context-Aware Button Management

        /// <summary>
        /// Configure ribbon for specific form types
        /// </summary>
        /// <param name="formType">Type of form (UserMaster, RoleMaster, PermissionManagement, etc.)</param>
        public void ConfigureForFormType(string formType)
        {
            try
            {
                switch (formType.ToLower())
                {
                    case "usermaster":
                        ConfigureForUserMaster();
                        break;
                    case "rolemaster":
                        ConfigureForRoleMaster();
                        break;
                    case "permissionmanagement":
                        ConfigureForPermissionManagement();
                        break;
                    case "database":
                        ConfigureForDatabase();
                        break;
                    case "parameters":
                        ConfigureForParameters();
                        break;
                    case "sqlquery":
                        ConfigureForSQLQuery();
                        break;
                    case "usermanagementlist":
                        ConfigureForUserManagementList();
                        break;
                    case "rolemanagement":
                        ConfigureForRoleManagement();
                        break;
                    default:
                        ConfigureDefault();
                        break;
                }
            }
            catch (Exception)
            {
                ConfigureDefault();
            }
        }

        /// <summary>
        /// Configure ribbon for User Master form
        /// </summary>
        private void ConfigureForUserMaster()
        {
            // Show all standard operations
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = true;
            ribbonPageGroup1.Visible = true; // Print group
            RibbonPageGroupGrid.Visible = false; // Hide grid operations for single record form

            // Update page title
            RibbonPageEstimate.Text = "User Management";
        }

        /// <summary>
        /// Configure ribbon for Role Master form
        /// </summary>
        private void ConfigureForRoleMaster()
        {
            // Show all standard operations
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = true;
            ribbonPageGroup1.Visible = true; // Print group
            RibbonPageGroupGrid.Visible = false; // Hide grid operations for single record form

            // Update page title
            RibbonPageEstimate.Text = "Role Management";
        }

        /// <summary>
        /// Configure ribbon for Permission Management form
        /// </summary>
        private void ConfigureForPermissionManagement()
        {
            // Hide navigation for permission management
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = true; // Print group
            RibbonPageGroupGrid.Visible = false; // Grid operations handled by form

            // Update page title
            RibbonPageEstimate.Text = "Permission Management";
        }

        /// <summary>
        /// Configure ribbon for Database form
        /// </summary>
        private void ConfigureForDatabase()
        {
            // Minimal operations for database form
            RibbonPageGroupOperations.Visible = false;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = false;
            RibbonPageGroupGrid.Visible = false;

            // Update page title
            RibbonPageEstimate.Text = "Database";
        }

        /// <summary>
        /// Configure ribbon for Parameters form
        /// </summary>
        private void ConfigureForParameters()
        {
            // Show operations but hide navigation
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = true;
            RibbonPageGroupGrid.Visible = true; // Parameters use grid

            // Update page title
            RibbonPageEstimate.Text = "Parameters";
        }

        /// <summary>
        /// Configure ribbon for SQL Query form
        /// </summary>
        private void ConfigureForSQLQuery()
        {
            // Minimal operations for SQL query form
            RibbonPageGroupOperations.Visible = false;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = true; // Keep print for query results
            RibbonPageGroupGrid.Visible = false;

            // Update page title
            RibbonPageEstimate.Text = "SQL Query";
        }

        /// <summary>
        /// Configure ribbon for User Management List form
        /// </summary>
        private void ConfigureForUserManagementList()
        {
            // Show operations and grid, hide navigation
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = true;
            RibbonPageGroupGrid.Visible = true;

            // Update page title
            RibbonPageEstimate.Text = "User List";
        }

        /// <summary>
        /// Configure ribbon for Role Management operations
        /// </summary>
        private void ConfigureForRoleManagement()
        {
            // Show operations for role management, hide navigation
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = false;
            ribbonPageGroup1.Visible = true; // Print group
            RibbonPageGroupGrid.Visible = true; // Grid operations for role list

            // Update page title
            RibbonPageEstimate.Text = "Role Management";
        }

        /// <summary>
        /// Default configuration
        /// </summary>
        private void ConfigureDefault()
        {
            // Show all groups by default
            RibbonPageGroupOperations.Visible = true;
            RibbonPageGroupNavigation.Visible = true;
            ribbonPageGroup1.Visible = true;
            RibbonPageGroupGrid.Visible = true;

            // Default page title
            RibbonPageEstimate.Text = "Operations";
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Force refresh of all permission-based states
        /// </summary>
        public void RefreshAll()
        {
            RefreshPermissions();
            UpdateButtonStates();
        }

        /// <summary>
        /// Check if user has specific permission for current form
        /// </summary>
        /// <param name="permissionType">Type of permission to check</param>
        /// <returns>True if user has permission</returns>
        public bool HasPermission(string permissionType)
        {
            // Skip permission checking in design mode
            if (DesignMode) return true;
            
            if (_currentPermissions == null) return false;

            string lowerType = permissionType.ToLower();
            switch (lowerType)
            {
                case "read":
                    return _currentPermissions.CanRead;
                case "create":
                case "new":
                    return _currentPermissions.CanCreate;
                case "edit":
                    return _currentPermissions.CanEdit;
                case "delete":
                    return _currentPermissions.CanDelete;
                case "print":
                    return _currentPermissions.CanPrint;
                default:
                    return false;
            }
        }

        #endregion

        #region Cleanup

        /// <summary>
        /// Clean up resources and unsubscribe from events
        /// Called from the Designer's Dispose method
        /// </summary>
        private void CleanupResources()
        {
            // Unsubscribe from global permission changes
            ProManage.Modules.Services.GlobalPermissionService.GlobalPermissionsChanged -= OnGlobalPermissionsChanged;
        }

        #endregion
    }
}
